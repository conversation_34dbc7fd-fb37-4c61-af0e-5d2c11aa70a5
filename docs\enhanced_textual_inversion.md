# Enhanced Textual Inversion with Knowledge Preservation Loss

## Overview

This implementation extends the standard Textual Inversion approach with sophisticated CLIP Text Encoder fine-tuning and Knowledge Preservation Loss to improve personalized text-to-image generation while maintaining semantic understanding.

## Key Innovations

### 1. Knowledge Preservation Loss

Instead of using a fixed anchor approach, our method implements dynamic knowledge preservation by maintaining both trainable and frozen copies of the CLIP Text Encoder:

```
Knowledge Preservation Loss = 1 - cosine_similarity(Trainable_output, Frozen_output)
```

**Benefits:**
- Prevents semantic drift during fine-tuning
- Maintains original language understanding
- Allows flexible adaptation to new concepts

### 2. Three-Component Loss Function

The total training loss combines three weighted components:

```
Total Loss = α×Diffusion Loss + γ×Regularization Loss + β×Knowledge Preservation Loss
```

Where:
- **α (diffusion_weight)**: Controls the main diffusion training objective
- **γ (regularization_weight)**: Prevents embedding drift from initialization  
- **β (knowledge_preservation_weight)**: Maintains semantic understanding

### 3. Configurable Layer Fine-tuning

The system allows selective fine-tuning of CLIP Text Encoder layers:

- **Default**: Fine-tune only the last transformer layer and layer norm
- **Selective**: Specify particular layers (e.g., `layers.10`, `layers.11`)
- **Full**: Fine-tune all transformer layers with lower learning rate

## Usage

### Basic Enhanced Training

```bash
python train_cross_init.py \
    --pretrained_model_name_or_path "runwayml/stable-diffusion-v1-5" \
    --train_data_dir "./data/person_images" \
    --placeholder_token "<person>" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --finetune_clip \
    --use_knowledge_preservation \
    --knowledge_preservation_weight 0.1 \
    --clip_lr 1e-5 \
    --max_train_steps 3000
```

### Advanced Configuration

```bash
python train_cross_init.py \
    --pretrained_model_name_or_path "runwayml/stable-diffusion-v1-5" \
    --train_data_dir "./data/person_images" \
    --placeholder_token "<person>" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --finetune_clip \
    --use_knowledge_preservation \
    --knowledge_preservation_weight 0.2 \
    --diffusion_weight 1.0 \
    --reg_weight 1e-5 \
    --finetune_layers "text_model.encoder.layers.10" "text_model.encoder.layers.11" \
    --clip_lr 5e-6 \
    --use_projection \
    --max_train_steps 5000
```

### Automated Experiments

Use the experiment runner for systematic evaluation:

```bash
# Run comparison study
python run_experiments.py \
    --train_data_dir "./data/person_images" \
    --placeholder_token "<person>" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --experiment_type comparison

# Run hyperparameter sweep
python run_experiments.py \
    --train_data_dir "./data/person_images" \
    --placeholder_token "<person>" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --experiment_type sweep
```

## Configuration System

### Experiment Configurations

The system provides predefined configurations for different experimental setups:

1. **Baseline**: Standard textual inversion without CLIP fine-tuning
2. **Enhanced**: Knowledge preservation with selective layer fine-tuning
3. **Full Fine-tuning**: Complete CLIP encoder adaptation

### Custom Configuration

Create custom experiment configurations:

```python
from configs.experiment_config import ExperimentConfig, LossConfig, CLIPFinetuningConfig

config = ExperimentConfig(
    experiment_name="custom_experiment",
    loss=LossConfig(
        knowledge_preservation_weight=0.15,
        diffusion_weight=1.0,
        regularization_weight=1e-5
    ),
    clip_finetuning=CLIPFinetuningConfig(
        enabled=True,
        finetune_layers=["text_model.encoder.layers.11"],
        learning_rate=1e-5
    )
)
```

## Evaluation Metrics

The system implements comprehensive evaluation metrics:

### 1. CLIP Score
Measures text-image alignment using CLIP similarity.

### 2. Identity Preservation
Evaluates how well the generated images preserve the identity of the training subject.

### 3. Semantic Understanding
Tests compositionality by evaluating how well the model handles different attributes and contexts.

### 4. Knowledge Preservation
Measures semantic drift by comparing embeddings from original and fine-tuned encoders.

## Hyperparameter Guidelines

### Knowledge Preservation Weight (β)
- **0.05-0.1**: Light preservation, more adaptation flexibility
- **0.1-0.2**: Balanced preservation and adaptation
- **0.2-0.5**: Strong preservation, limited adaptation

### CLIP Learning Rate
- **1e-6**: Very conservative, minimal changes
- **5e-6**: Recommended for full fine-tuning
- **1e-5**: Standard for selective layer fine-tuning
- **2e-5**: Aggressive, may cause instability

### Layer Selection
- **Last layer only**: Minimal impact, safe adaptation
- **Last 2-3 layers**: Balanced approach, recommended
- **All layers**: Maximum flexibility, requires careful tuning

## Implementation Details

### CLIPTextEncoderWithKnowledgePreservation

The core implementation maintains dual encoders:

```python
class CLIPTextEncoderWithKnowledgePreservation(nn.Module):
    def __init__(self, text_encoder, dim=1024, finetune_layers=None):
        self.trainable_encoder = text_encoder
        self.frozen_encoder = self._create_frozen_copy(text_encoder)
        # ... configuration and projection layers
```

### Loss Computation

The enhanced loss computation integrates all components:

```python
total_loss = (
    diffusion_weight * diffusion_loss +
    reg_weight * reg_loss +
    knowledge_preservation_weight * knowledge_preservation_loss
)
```

## Results and Analysis

### Expected Improvements

1. **Better Semantic Compositionality**: Enhanced ability to combine learned concepts with various attributes
2. **Reduced Overfitting**: Knowledge preservation prevents excessive adaptation
3. **Improved Generalization**: Better performance on diverse prompts and contexts
4. **Maintained Language Understanding**: Preserved general text comprehension

### Monitoring Training

Key metrics to monitor during training:

- **Total Loss**: Overall training objective
- **Diffusion Loss**: Core image generation quality
- **Regularization Loss**: Embedding stability
- **Knowledge Preservation Loss**: Semantic drift prevention

## Troubleshooting

### Common Issues

1. **High Knowledge Preservation Loss**: Reduce β weight or increase CLIP learning rate
2. **Poor Concept Learning**: Increase training steps or reduce preservation weight
3. **Semantic Drift**: Increase preservation weight or reduce CLIP learning rate
4. **Training Instability**: Use gradient clipping and lower learning rates

### Performance Optimization

- Use mixed precision training (`--mixed_precision fp16`)
- Enable gradient checkpointing for memory efficiency
- Use xFormers for attention optimization
- Adjust batch size based on GPU memory

## Future Enhancements

1. **Advanced Pooling Strategies**: Beyond mean pooling for knowledge preservation
2. **Adaptive Weight Scheduling**: Dynamic adjustment of loss weights during training
3. **Multi-Scale Knowledge Preservation**: Preservation at different semantic levels
4. **Cross-Modal Knowledge Transfer**: Leveraging vision-language alignment

## References

- Original Textual Inversion: [An Image is Worth One Word](https://arxiv.org/abs/2208.01618)
- CLIP: [Learning Transferable Visual Representations](https://arxiv.org/abs/2103.00020)
- Stable Diffusion: [High-Resolution Image Synthesis](https://arxiv.org/abs/2112.10752)
