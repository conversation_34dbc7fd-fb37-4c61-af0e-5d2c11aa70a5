
-- Fixed true/false fusion
python train_cross_init.py \
    --save_steps 600 \
    --only_save_embeds \
    --placeholder_token "<18525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day220525_500_HaveFusionTransformerFW1_HaveFinetuneCLIP_TLW0005_Celeb3/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --use_fusion \
    --fusion_weight 1 \
    --use_transformer \
    --finetune_clip \
    --clip_lr 1e-5 \
    --clip_weight_decay 0.01 \
    --text_loss_weight 0.005
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day220525_500_HaveFusionTransformerFW1_HaveFinetuneCLIP_TLW0005_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a photo of {} person" \
    --save_dir "./logs/IMAGES/Day220525_500_HaveFusionTransformerFW1_HaveFinetuneCLIP_TLW0005_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day220525_500_HaveFusionTransformerFW1_HaveFinetuneCLIP_TLW0005_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person is eating her birthday cake" \
    --save_dir "./logs/IMAGES/Day220525_500_HaveFusionTransformerFW1_HaveFinetuneCLIP_TLW0005_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day220525_500_HaveFusionTransformerFW1_HaveFinetuneCLIP_TLW0005_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person with angry expression" \
    --save_dir "./logs/IMAGES/Day220525_500_HaveFusionTransformerFW1_HaveFinetuneCLIP_TLW0005_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42

--------- used fusion but not finetune
python train_cross_init.py \
    --save_steps 600 \
    --only_save_embeds \
    --placeholder_token "<18525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day220525_500_HaveFusionTransformerFW1_NoFinetuneCLIP_Celeb3/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --use_fusion \
    --fusion_weight 1 \
    --use_transformer
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day220525_500_HaveFusionTransformerFW1_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person is eating her birthday cake" \
    --save_dir "./logs/IMAGES/Day220525_500_HaveFusionTransformerFW1_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day220525_500_HaveFusionTransformerFW1_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person with angry expression" \
    --save_dir "./logs/IMAGES/Day220525_500_HaveFusionTransformerFW1_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42

-- chạy mà chưa test
python train_cross_init.py \
    --save_steps 600 \
    --only_save_embeds \
    --placeholder_token "<18525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day220525_500_HaveFusionTransformerFW08_NoFinetuneCLIP_Celeb3/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --use_fusion \
    --fusion_weight 0.8 \
    --use_transformer
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day220525_500_HaveFusionTransformerFW08_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person is eating her birthday cake" \
    --save_dir "./logs/IMAGES/Day220525_500_HaveFusionTransformerFW08_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42
python train_cross_init.py \
    --save_steps 600 \
    --only_save_embeds \
    --placeholder_token "<18525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day220525_500_HaveFusionTransformerFW05_NoFinetuneCLIP_Celeb3/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --use_fusion \
    --fusion_weight 0.5 \
    --use_transformer
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day220525_500_HaveFusionTransformerFW05_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person is eating her birthday cake" \
    --save_dir "./logs/IMAGES/Day220525_500_HaveFusionTransformerFW05_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42

python train_cross_init.py \
    --save_steps 600 \
    --only_save_embeds \
    --placeholder_token "<18525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day220525_500_NoFusion_HaveFinetuneCLIP_TLW005_Celeb3/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --finetune_clip \
    --clip_lr 1e-5 \
    --clip_weight_decay 0.01 \
    --text_loss_weight 0.005
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day220525_500_NoFusion_HaveFinetuneCLIP_TLW005_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person is eating her birthday cake" \
    --save_dir "./logs/IMAGES/Day220525_500_NoFusion_HaveFinetuneCLIP_TLW005_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42


python train_cross_init.py \
    --save_steps 600 \
    --only_save_embeds \
    --placeholder_token "<18525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day220525_500_NoFusion_HaveFinetuneCLIP_TLW1e5_Celeb3/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --finetune_clip \
    --clip_lr 1e-5 \
    --clip_weight_decay 0.01 \
    --text_loss_weight 0.00001
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day220525_500_NoFusion_HaveFinetuneCLIP_TLW1e5_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person is eating her birthday cake" \
    --save_dir "./logs/IMAGES/Day220525_500_NoFusion_HaveFinetuneCLIP_TLW1e5_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42




python train_cross_init.py \
    --save_steps 600 \
    --only_save_embeds \
    --placeholder_token "<18525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day220525_500_NoFusion_AddedLoRA_Celeb3/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4