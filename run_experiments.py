#!/usr/bin/env python3
"""
Comprehensive Experiment Runner for Enhanced Textual Inversion with Knowledge Preservation Loss.

This script provides automated experiment execution, hyperparameter sweeps, and evaluation
for the enhanced textual inversion approach.
"""

import os
import sys
import json
import argparse
import subprocess
from pathlib import Path
from typing import List, Dict, Any
import time
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from configs.experiment_config import (
    ExperimentConfig, 
    get_baseline_config, 
    get_enhanced_config, 
    get_full_finetuning_config,
    get_hyperparameter_sweep_configs
)
from evaluation.metrics import ComprehensiveEvaluator, load_images_from_directory


class ExperimentRunner:
    """Main experiment runner class."""
    
    def __init__(self, base_output_dir: str = "./experiments"):
        self.base_output_dir = Path(base_output_dir)
        self.base_output_dir.mkdir(exist_ok=True)
        self.evaluator = ComprehensiveEvaluator()
    
    def run_single_experiment(self, config: ExperimentConfig, 
                            train_data_dir: str, 
                            placeholder_token: str,
                            celeb_path: str = None,
                            initialize_tokens: List[str] = None) -> Dict[str, Any]:
        """Run a single experiment with the given configuration."""
        
        # Create experiment directory
        exp_dir = self.base_output_dir / config.experiment_name
        exp_dir.mkdir(exist_ok=True)
        
        # Save configuration
        config_path = exp_dir / "config.json"
        config.save(str(config_path))
        
        # Prepare training command
        cmd = self._build_training_command(config, train_data_dir, placeholder_token, 
                                         celeb_path, initialize_tokens, str(exp_dir))
        
        print(f"Starting experiment: {config.experiment_name}")
        print(f"Command: {' '.join(cmd)}")
        
        # Run training
        start_time = time.time()
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            training_success = True
            training_output = result.stdout
        except subprocess.CalledProcessError as e:
            training_success = False
            training_output = e.stderr
            print(f"Training failed for {config.experiment_name}: {e}")
        
        training_time = time.time() - start_time
        
        # Evaluate results if training succeeded
        evaluation_results = {}
        if training_success:
            try:
                evaluation_results = self._evaluate_experiment(config, exp_dir)
            except Exception as e:
                print(f"Evaluation failed for {config.experiment_name}: {e}")
        
        # Compile experiment results
        experiment_results = {
            "experiment_name": config.experiment_name,
            "config": config.__dict__,
            "training_success": training_success,
            "training_time": training_time,
            "training_output": training_output,
            "evaluation_results": evaluation_results,
            "timestamp": datetime.now().isoformat()
        }
        
        # Save results
        results_path = exp_dir / "results.json"
        with open(results_path, 'w') as f:
            json.dump(experiment_results, f, indent=2)
        
        return experiment_results
    
    def _build_training_command(self, config: ExperimentConfig, 
                              train_data_dir: str, 
                              placeholder_token: str,
                              celeb_path: str,
                              initialize_tokens: List[str],
                              output_dir: str) -> List[str]:
        """Build the training command from configuration."""
        
        cmd = [
            "python", "train_cross_init.py",
            "--pretrained_model_name_or_path", config.training.pretrained_model_name_or_path,
            "--train_data_dir", train_data_dir,
            "--placeholder_token", placeholder_token,
            "--output_dir", output_dir,
            "--resolution", str(config.training.resolution),
            "--train_batch_size", str(config.training.train_batch_size),
            "--gradient_accumulation_steps", str(config.training.gradient_accumulation_steps),
            "--max_train_steps", str(config.training.max_train_steps),
            "--learning_rate", str(config.training.learning_rate),
            "--n_persudo_tokens", str(config.training.n_pseudo_tokens),
            "--validation_steps", str(config.training.validation_steps),
            "--save_steps", str(config.training.save_steps),
            "--mixed_precision", config.training.mixed_precision,
        ]
        
        # Add initialization method
        if initialize_tokens:
            cmd.extend(["--initialize_tokens"] + initialize_tokens)
        elif celeb_path:
            cmd.extend(["--celeb_path", celeb_path])
        
        # Add CLIP fine-tuning parameters
        if config.clip_finetuning.enabled:
            cmd.append("--finetune_clip")
            cmd.extend([
                "--clip_lr", str(config.clip_finetuning.learning_rate),
                "--clip_weight_decay", str(config.clip_finetuning.weight_decay)
            ])
            
            if config.clip_finetuning.finetune_layers:
                if isinstance(config.clip_finetuning.finetune_layers, list):
                    cmd.extend(["--finetune_layers"] + config.clip_finetuning.finetune_layers)
                else:
                    cmd.extend(["--finetune_layers", config.clip_finetuning.finetune_layers])
            
            if config.clip_finetuning.use_projection:
                cmd.append("--use_projection")
        
        # Add knowledge preservation parameters
        if config.loss.use_knowledge_preservation:
            cmd.extend([
                "--use_knowledge_preservation",
                "--knowledge_preservation_weight", str(config.loss.knowledge_preservation_weight),
                "--diffusion_weight", str(config.loss.diffusion_weight),
                "--reg_weight", str(config.loss.regularization_weight)
            ])
        
        # Add validation prompts
        if config.evaluation.validation_prompts:
            validation_file = Path(output_dir) / "validation_prompts.txt"
            with open(validation_file, 'w') as f:
                for prompt in config.evaluation.validation_prompts:
                    f.write(prompt.format(placeholder_token) + "\n")
            cmd.extend(["--validation_prompt_file", str(validation_file)])
        
        # Add other training options
        if config.training.gradient_checkpointing:
            cmd.append("--gradient_checkpointing")
        
        if config.training.enable_xformers:
            cmd.append("--enable_xformers_memory_efficient_attention")
        
        return cmd
    
    def _evaluate_experiment(self, config: ExperimentConfig, exp_dir: Path) -> Dict[str, Any]:
        """Evaluate the results of an experiment."""
        
        # Look for generated validation images
        validation_dir = exp_dir / "validation"
        if not validation_dir.exists():
            return {"error": "No validation images found"}
        
        # Load generated images
        generated_images = load_images_from_directory(str(validation_dir))
        if not generated_images:
            return {"error": "No generated images found"}
        
        # Prepare prompts (use validation prompts from config)
        prompts = [prompt.format(config.training.placeholder_token) 
                  for prompt in config.evaluation.validation_prompts[:len(generated_images)]]
        
        # Load reference images if available
        reference_images = None
        if config.evaluation.reference_images_dir:
            reference_images = load_images_from_directory(config.evaluation.reference_images_dir)
        
        # Run comprehensive evaluation
        evaluation_results = self.evaluator.evaluate(
            generated_images=generated_images,
            prompts=prompts,
            reference_images=reference_images
        )
        
        return evaluation_results
    
    def run_hyperparameter_sweep(self, train_data_dir: str, 
                                placeholder_token: str,
                                celeb_path: str = None,
                                initialize_tokens: List[str] = None) -> List[Dict[str, Any]]:
        """Run a comprehensive hyperparameter sweep."""
        
        configs = get_hyperparameter_sweep_configs()
        results = []
        
        for config in configs:
            try:
                result = self.run_single_experiment(
                    config, train_data_dir, placeholder_token, 
                    celeb_path, initialize_tokens
                )
                results.append(result)
            except Exception as e:
                print(f"Failed to run experiment {config.experiment_name}: {e}")
                results.append({
                    "experiment_name": config.experiment_name,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
        
        # Save sweep results
        sweep_results_path = self.base_output_dir / "hyperparameter_sweep_results.json"
        with open(sweep_results_path, 'w') as f:
            json.dump(results, f, indent=2)
        
        return results
    
    def run_comparison_study(self, train_data_dir: str, 
                           placeholder_token: str,
                           celeb_path: str = None,
                           initialize_tokens: List[str] = None) -> List[Dict[str, Any]]:
        """Run a comparison study between different approaches."""
        
        configs = [
            get_baseline_config(),
            get_enhanced_config(),
            get_full_finetuning_config()
        ]
        
        results = []
        for config in configs:
            try:
                result = self.run_single_experiment(
                    config, train_data_dir, placeholder_token,
                    celeb_path, initialize_tokens
                )
                results.append(result)
            except Exception as e:
                print(f"Failed to run experiment {config.experiment_name}: {e}")
        
        return results


def main():
    parser = argparse.ArgumentParser(description="Run Enhanced Textual Inversion Experiments")
    parser.add_argument("--train_data_dir", required=True, help="Training data directory")
    parser.add_argument("--placeholder_token", required=True, help="Placeholder token (e.g., <concept>)")
    parser.add_argument("--celeb_path", help="Path to celebrity names file")
    parser.add_argument("--initialize_tokens", nargs="*", help="Initialization tokens")
    parser.add_argument("--experiment_type", choices=["single", "sweep", "comparison"], 
                       default="comparison", help="Type of experiment to run")
    parser.add_argument("--config_path", help="Path to experiment configuration file")
    parser.add_argument("--output_dir", default="./experiments", help="Output directory")
    
    args = parser.parse_args()
    
    runner = ExperimentRunner(args.output_dir)
    
    if args.experiment_type == "single":
        if args.config_path:
            config = ExperimentConfig.load(args.config_path)
        else:
            config = get_enhanced_config()
        
        result = runner.run_single_experiment(
            config, args.train_data_dir, args.placeholder_token,
            args.celeb_path, args.initialize_tokens
        )
        print(f"Experiment completed: {result['experiment_name']}")
        
    elif args.experiment_type == "sweep":
        results = runner.run_hyperparameter_sweep(
            args.train_data_dir, args.placeholder_token,
            args.celeb_path, args.initialize_tokens
        )
        print(f"Hyperparameter sweep completed: {len(results)} experiments")
        
    elif args.experiment_type == "comparison":
        results = runner.run_comparison_study(
            args.train_data_dir, args.placeholder_token,
            args.celeb_path, args.initialize_tokens
        )
        print(f"Comparison study completed: {len(results)} experiments")


if __name__ == "__main__":
    main()
