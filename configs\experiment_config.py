"""
Experiment Configuration System for Enhanced Textual Inversion with Knowledge Preservation Loss.

This module provides comprehensive configuration management for different experimental setups,
hyperparameter tuning, and evaluation protocols.
"""

import json
import os
from dataclasses import dataclass, asdict
from typing import List, Optional, Dict, Any


@dataclass
class LossConfig:
    """Configuration for loss function components."""
    # Loss weights (α, β, γ in the paper)
    diffusion_weight: float = 1.0          # α - weight for diffusion loss
    knowledge_preservation_weight: float = 0.1  # β - weight for knowledge preservation loss
    regularization_weight: float = 1e-5    # γ - weight for regularization loss

    # Knowledge preservation loss settings
    use_knowledge_preservation: bool = True
    preservation_loss_type: str = "cosine"  # "cosine", "mse", "kl_div"
    preservation_pooling: str = "mean"      # "mean", "cls", "max"


@dataclass
class CLIPFinetuningConfig:
    """Configuration for CLIP text encoder fine-tuning."""
    # Whether to enable CLIP fine-tuning
    enabled: bool = True

    # Which layers to fine-tune
    finetune_layers: Optional[List[str]] = None  # None for default, "all" for all layers

    # Whether to use projection layers
    use_projection: bool = True
    projection_dim: int = 1024

    # Learning rate and optimization
    learning_rate: float = 1e-5
    weight_decay: float = 0.01

    # Scheduler settings
    lr_scheduler: str = "cosine"
    warmup_steps: int = 500


@dataclass
class TrainingConfig:
    """Main training configuration."""
    # Basic training parameters
    max_train_steps: int = 5000
    train_batch_size: int = 8
    gradient_accumulation_steps: int = 1
    learning_rate: float = 1e-4

    # Model and data
    pretrained_model_name_or_path: str = "runwayml/stable-diffusion-v1-5"
    resolution: int = 512

    # Textual inversion specific
    n_pseudo_tokens: int = 2
    placeholder_token: str = "<concept>"

    # Validation and logging
    validation_steps: int = 100
    save_steps: int = 500
    checkpointing_steps: int = 500

    # Mixed precision and optimization
    mixed_precision: str = "fp16"
    gradient_checkpointing: bool = True
    enable_xformers: bool = True


@dataclass
class EvaluationConfig:
    """Configuration for evaluation metrics and protocols."""
    # Validation prompts for different aspects
    validation_prompts: List[str] = None

    # Number of images to generate for evaluation
    num_validation_images: int = 4
    num_inference_steps: int = 50

    # Evaluation metrics to compute
    compute_clip_score: bool = True
    compute_fid: bool = True
    compute_lpips: bool = True
    compute_identity_preservation: bool = True

    # Reference images for identity preservation
    reference_images_dir: Optional[str] = None

    def __post_init__(self):
        if self.validation_prompts is None:
            self.validation_prompts = [
                "a photo of a {} person",
                "a portrait of a {} person",
                "a {} person smiling",
                "a {} person in a suit",
                "a {} person outdoors"
            ]


@dataclass
class ExperimentConfig:
    """Complete experiment configuration."""
    # Experiment metadata
    experiment_name: str = "enhanced_textual_inversion"
    description: str = "Textual Inversion with Knowledge Preservation Loss"

    # Component configurations
    training: TrainingConfig = None
    loss: LossConfig = None
    clip_finetuning: CLIPFinetuningConfig = None
    evaluation: EvaluationConfig = None

    # Output directories
    output_dir: str = "./experiments"
    logging_dir: str = "./logs"

    def __post_init__(self):
        if self.training is None:
            self.training = TrainingConfig()
        if self.loss is None:
            self.loss = LossConfig()
        if self.clip_finetuning is None:
            self.clip_finetuning = CLIPFinetuningConfig()
        if self.evaluation is None:
            self.evaluation = EvaluationConfig()

    def save(self, filepath: str):
        """Save configuration to JSON file."""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w') as f:
            json.dump(asdict(self), f, indent=2)

    @classmethod
    def load(cls, filepath: str):
        """Load configuration from JSON file."""
        with open(filepath, 'r') as f:
            data = json.load(f)
        return cls(**data)


# Predefined experiment configurations
def get_baseline_config() -> ExperimentConfig:
    """Get baseline configuration (standard textual inversion)."""
    config = ExperimentConfig(
        experiment_name="baseline_textual_inversion",
        description="Standard Textual Inversion without CLIP fine-tuning"
    )
    config.clip_finetuning.enabled = False
    config.loss.use_knowledge_preservation = False
    return config


def get_enhanced_config() -> ExperimentConfig:
    """Get enhanced configuration with knowledge preservation."""
    config = ExperimentConfig(
        experiment_name="enhanced_with_knowledge_preservation",
        description="Enhanced Textual Inversion with Knowledge Preservation Loss"
    )
    config.loss.knowledge_preservation_weight = 0.1
    config.clip_finetuning.finetune_layers = ['text_model.encoder.layers.10', 'text_model.encoder.layers.11']
    return config


def get_full_finetuning_config() -> ExperimentConfig:
    """Get configuration for full CLIP fine-tuning."""
    config = ExperimentConfig(
        experiment_name="full_clip_finetuning",
        description="Full CLIP Text Encoder Fine-tuning with Knowledge Preservation"
    )
    config.loss.knowledge_preservation_weight = 0.2
    config.clip_finetuning.finetune_layers = "all"
    config.clip_finetuning.learning_rate = 5e-6  # Lower LR for full fine-tuning
    return config


def get_identity_focused_config() -> ExperimentConfig:
    """Get configuration optimized for strong identity preservation."""
    config = ExperimentConfig(
        experiment_name="identity_focused_training",
        description="Optimized for strong personal identity preservation with balanced knowledge preservation"
    )

    # Training settings optimized for identity learning
    config.training.max_train_steps = 1500  # Increased for better identity learning
    config.training.learning_rate = 5e-4    # Slightly lower for stability
    config.training.train_batch_size = 2    # Match user's setup
    config.training.gradient_accumulation_steps = 4

    # Loss configuration for identity preservation
    config.loss.knowledge_preservation_weight = 0.02  # Very light preservation initially
    config.loss.regularization_weight = 5e-6          # Reduced regularization
    config.loss.diffusion_weight = 1.0

    # Conservative CLIP fine-tuning
    config.clip_finetuning.enabled = True
    config.clip_finetuning.learning_rate = 2e-6       # Much lower CLIP LR
    config.clip_finetuning.weight_decay = 0.005       # Reduced weight decay
    config.clip_finetuning.finetune_layers = ['text_model.encoder.layers.11']  # Only last layer

    return config


def get_progressive_training_configs() -> List[ExperimentConfig]:
    """Get configurations for progressive training strategy."""
    configs = []

    # Stage 1: Identity establishment (no knowledge preservation)
    stage1 = ExperimentConfig(
        experiment_name="stage1_identity_establishment",
        description="Stage 1: Establish identity without knowledge preservation"
    )
    stage1.training.max_train_steps = 800
    stage1.training.learning_rate = 5e-4
    stage1.loss.use_knowledge_preservation = False
    stage1.loss.regularization_weight = 1e-6  # Very light regularization
    stage1.clip_finetuning.enabled = False    # No CLIP fine-tuning initially
    configs.append(stage1)

    # Stage 2: Light knowledge preservation
    stage2 = ExperimentConfig(
        experiment_name="stage2_light_preservation",
        description="Stage 2: Add light knowledge preservation"
    )
    stage2.training.max_train_steps = 1200
    stage2.training.learning_rate = 3e-4
    stage2.loss.knowledge_preservation_weight = 0.01  # Very light
    stage2.loss.regularization_weight = 5e-6
    stage2.clip_finetuning.enabled = True
    stage2.clip_finetuning.learning_rate = 1e-6       # Very conservative
    configs.append(stage2)

    # Stage 3: Balanced training
    stage3 = ExperimentConfig(
        experiment_name="stage3_balanced_training",
        description="Stage 3: Balanced identity and knowledge preservation"
    )
    stage3.training.max_train_steps = 1500
    stage3.training.learning_rate = 2e-4
    stage3.loss.knowledge_preservation_weight = 0.03
    stage3.loss.regularization_weight = 1e-5
    stage3.clip_finetuning.learning_rate = 2e-6
    configs.append(stage3)

    return configs


def get_hyperparameter_sweep_configs() -> List[ExperimentConfig]:
    """Get configurations for hyperparameter sweep focused on identity preservation."""
    base_config = get_identity_focused_config()
    configs = []

    # Sweep over knowledge preservation weights (conservative range)
    for kp_weight in [0.01, 0.02, 0.03, 0.05]:
        config = ExperimentConfig(**asdict(base_config))
        config.experiment_name = f"identity_kp_weight_{kp_weight}"
        config.loss.knowledge_preservation_weight = kp_weight
        configs.append(config)

    # Sweep over CLIP learning rates (conservative range)
    for clip_lr in [1e-6, 2e-6, 5e-6, 1e-5]:
        config = ExperimentConfig(**asdict(base_config))
        config.experiment_name = f"identity_clip_lr_{clip_lr}"
        config.clip_finetuning.learning_rate = clip_lr
        configs.append(config)

    # Sweep over training steps
    for steps in [1000, 1500, 2000, 2500]:
        config = ExperimentConfig(**asdict(base_config))
        config.experiment_name = f"identity_steps_{steps}"
        config.training.max_train_steps = steps
        configs.append(config)

    return configs


if __name__ == "__main__":
    # Example usage
    config = get_enhanced_config()
    config.save("configs/enhanced_experiment.json")
    print("Saved enhanced experiment configuration")
