#!/usr/bin/env python
# coding=utf-8
# Copyright 2023 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and

import argparse
import logging
import math
import os
import warnings
from pathlib import Path
from typing import Optional

import numpy as np
import torch
import torch.nn.functional as F
import torch.utils.checkpoint
import transformers
from accelerate import Accelerator
from accelerate.logging import get_logger
from accelerate.utils import ProjectConfiguration, set_seed
from huggingface_hub import HfFolder, Repository, create_repo, whoami

# TODO: remove and import from diffusers.utils when the new version of diffusers is released
from packaging import version
from PIL import Image
from tqdm.auto import tqdm
from transformers import CLIP<PERSON>okenizer
from models.clip_model import CLIPTextModel

import diffusers
from diffusers import (
    AutoencoderKL,
    DDPMScheduler,
    DiffusionPipeline,
    DPMSolverMultistepScheduler,
    StableDiffusionPipeline,
    UNet2DConditionModel,
)
from diffusers.optimization import get_scheduler
from diffusers.utils import check_min_version
from diffusers.utils.import_utils import is_xformers_available

from textual_inversion_dataset import TextualInversionDataset
from utils import *
import json
# ------------------------------------------------------------------------------


# Will error if the minimal version of diffusers is not installed. Remove at your own risks.
check_min_version("0.15.0.dev0")

logger = get_logger(__name__)


def log_validation(text_encoder, tokenizer, unet, vae, args, accelerator, weight_dtype, step, placeholder_tokens):
    logger.info(
        f"Running validation... \n Generating {args.num_validation_images} images with prompt:"
        f" {args.validation_prompt}."
    )
    validation_output_dir=os.path.join(args.output_dir,"validation")
    if not os.path.exists(validation_output_dir):
        os.makedirs(validation_output_dir)
    # create pipeline (note: unet and vae are loaded again in float32)
    pipeline = DiffusionPipeline.from_pretrained( #Duong -- Load mô hình DiffusionPipeline để tạo ảnh từ văn bản.
        args.pretrained_model_name_or_path,
        text_encoder=accelerator.unwrap_model(text_encoder),
        tokenizer=tokenizer,
        unet=unet, #Duong --Mạng chính dùng để tạo ảnh
        vae=vae, #Duong --(Variational Autoencoder): Giúp tăng độ phân giải ảnh
        revision=args.revision,
        torch_dtype=weight_dtype,
    )
    pipeline.scheduler = DPMSolverMultistepScheduler.from_config(pipeline.scheduler.config)
    pipeline = pipeline.to(accelerator.device)
    pipeline.set_progress_bar_config(disable=True)

    # run inference  Duong -- Chạy suy luận (Inference) để tạo ảnh -- Nếu có args.seed, tạo một random generator để đảm bảo ảnh tái tạo được.
    generator = None if args.seed is None else torch.Generator(device=accelerator.device).manual_seed(args.seed)

    for i, prompt in enumerate(args.validation_prompt): #Duyệt qua danh sách validation_prompt. Chạy pipeline để tạo ảnh với 50 bước suy luận
        prompt=prompt.format(' '.join(placeholder_tokens))

        image = pipeline(prompt, num_inference_steps=50, generator=generator,num_images_per_prompt=args.num_validation_images).images
        image=image_grid(image,1,len(image))
        image.save(os.path.join(validation_output_dir,f'{"_".join(prompt.split(" "))}_step_{step}.jpg'))

    del pipeline #Giải phóng bộ nhớ GPU
    torch.cuda.empty_cache()


#Duong -- Lưu lại embeddings của token giả định vào file .bin.
def save_progress(text_encoder, placeholder_tokens, placeholder_token_ids, accelerator, args, save_path):
    logger.info("Saving embeddings")
    learned_embeds_dict = dict()
    token_embeds=accelerator.unwrap_model(text_encoder).get_input_embeddings().weight
    for token, id in zip(placeholder_tokens, placeholder_token_ids):
        learned_embeds = token_embeds[id]
        learned_embeds_dict[token] = learned_embeds.detach().cpu()
    torch.save(learned_embeds_dict, save_path)
    with open(os.path.join(os.path.dirname(save_path), "config.json"), "w") as f:
        json.dump(args.__dict__, f, indent=2)

#Duong -- Tạo tên repo trên Hugging Face Model Hub.
def get_full_repo_name(model_id: str, organization: Optional[str] = None, token: Optional[str] = None):
    if token is None:
        token = HfFolder.get_token()
    if organization is None:
        username = whoami(token)["name"]
        return f"{username}/{model_id}"
    else:
        return f"{organization}/{model_id}"

def train():
    #Bước 1: Đọc tham số
    args = parse_args()

    print("Start:-------------------------------------")
    # print("đang in những tham số trong lệnh nhập vào")
    # print(args)
    # print("end:-------------------------------------")
    # exit()

    logging_dir = os.path.join(args.output_dir, args.logging_dir)

    # Bước 2: Khởi tạo Accelerator : Tăng tốc huấn luyện trên nhiều GPU.
    accelerator_project_config = ProjectConfiguration(total_limit=args.checkpoints_total_limit)

    accelerator = Accelerator(
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        mixed_precision=args.mixed_precision,
        log_with=args.report_to,
        project_dir=logging_dir,
        project_config=accelerator_project_config,
    )

    # print(accelerator.num_processes)
    # exit()

    # Bước 3 :Ghi log thông tin huấn luyện.
    # Make one log on every process with the configuration for debugging.
    logging.basicConfig(
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        datefmt="%m/%d/%Y %H:%M:%S",
        level=logging.INFO,
    )
    logger.info(accelerator.state, main_process_only=False)
    # print(accelerator.is_local_main_process)
    # exit()
    if accelerator.is_local_main_process:
        transformers.utils.logging.set_verbosity_warning()
        diffusers.utils.logging.set_verbosity_info()
    else:
        transformers.utils.logging.set_verbosity_error()
        diffusers.utils.logging.set_verbosity_error()

    # If passed along, set the training seed now.
    if args.seed is not None:
        set_seed(args.seed)
        setup_seed(args.seed)

    #  Bước 4 :Khởi tạo repo trên Hugging Face ------------------
    # Handle the repository creation
    # print(args.push_to_hub)
    # exit()
    if accelerator.is_main_process:
        if args.push_to_hub:
            if args.hub_model_id is None:
                repo_name = get_full_repo_name(Path(args.output_dir).name, token=args.hub_token)
            else:
                repo_name = args.hub_model_id
            create_repo(repo_name, exist_ok=True, token=args.hub_token)
            repo = Repository(args.output_dir, clone_from=repo_name, token=args.hub_token)

            with open(os.path.join(args.output_dir, ".gitignore"), "w+") as gitignore:
                if "step_*" not in gitignore:
                    gitignore.write("step_*\n")
                if "epoch_*" not in gitignore:
                    gitignore.write("epoch_*\n")
        elif args.output_dir is not None:
            os.makedirs(args.output_dir, exist_ok=True)

    # Load tokenizer  Biến đổi văn bản thành token số
    if args.tokenizer_name:
        tokenizer = CLIPTokenizer.from_pretrained(args.tokenizer_name)
    elif args.pretrained_model_name_or_path:
        tokenizer = CLIPTokenizer.from_pretrained(args.pretrained_model_name_or_path, subfolder="tokenizer")
    # print(tokenizer)
    # exit()

    # Bước 5: Load Tokenizer và Mô hình   ---------------------------------------------------------
    #     ✅ Chức năng:
    # Text Encoder (CLIP) chuyển đổi văn bản thành vector số.
    # Noise Scheduler điều chỉnh lượng nhiễu trong quá trình sinh ảnh.
    # UNet sử dụng vector text + nhiễu để dần tạo ra ảnh.
    # VAE giúp nén/gải mã ảnh để tăng hiệu suất.
    # Load scheduler and models
    noise_scheduler = DDPMScheduler.from_pretrained(args.pretrained_model_name_or_path, subfolder="scheduler")
    text_encoder = CLIPTextModel.from_pretrained(
        args.pretrained_model_name_or_path, subfolder="text_encoder", revision=args.revision
    )#subfolder="text_encoder": Chỉ định load từ thư mục "text_encoder".
    #Lúc này text_encoder được load với vocabulary mặc định của model pretrained.
    # Text encoder được load trước để lấy kiến trúc và weights của model gốc
    # print(text_encoder)
    # exit()
    #revision=args.revision: Chọn phiên bản của mô hình nếu có nhiều bản khác nhau.

    vae = AutoencoderKL.from_pretrained(args.pretrained_model_name_or_path, subfolder="vae", revision=args.revision) #Bộ mã hóa tự động biến ảnh thành không gian latent (nén ảnh) và giải mã lại thành ảnh.
    unet = UNet2DConditionModel.from_pretrained( #Mạng UNet chính trong mô hình khuếch tán, chịu trách nhiệm tạo ảnh từ nhiễu.Load mô hình UNet (Lõi chính của Stable Diffusion)
        args.pretrained_model_name_or_path, subfolder="unet", revision=args.revision
    )

    #Bước 6: Thêm token mới vào Tokenizer
    # Add the placeholder token in tokenizer
    placeholder_tokens = []
    placeholder_token_ids = []
    # print("tokenizer::::::::::::")
    # print("Vocab size before:", tokenizer.vocab_size)
    # print(tokenizer)
    # print("Vocab size before:", tokenizer.vocab_size)
    # tokenizer.add_tokens("<thuyduong>_v0")
    # print("Vocab size after:", tokenizer.vocab_size)

    for id in range(args.n_persudo_tokens):
        new_token=args.placeholder_token+f'_v{id}'
        num_added_tokens = tokenizer.add_tokens(new_token)
        if num_added_tokens ==0:
            raise ValueError(
                f"The tokenizer already contains the token {args.placeholder_token}. Please pass a different"
                " `placeholder_token` that is not already in the tokenizer."
            )
        placeholder_tokens.append(new_token)
        placeholder_token_ids.append(tokenizer.convert_tokens_to_ids(new_token))
        # Thêm vào tokenizer và lấy IDs:
        # tokenizer.add_tokens("<thuyduong>_v0")  # Ví dụ: ID = 49408
        # tokenizer.add_tokens("<thuyduong>_v1")  # Ví dụ: ID = 49409

    # print("placeholder_tokens::::::::::::")
    # print(placeholder_tokens)
    # print("tokenizer::::::::::::")
    # print("Vocab size aftẻr:", tokenizer.vocab_size)
    # print(tokenizer)
    # exit()


    # Resize the token embeddings as we are adding new special tokens to the tokenizer
    # Trước khi resize:
    print("Trước khi resize:")
    print(text_encoder.get_input_embeddings().weight.shape)
    # Ví dụ: torch.Size([49408, 768])  # 49408 tokens gốc, mỗi token có vector 768 chiều
    # vì đã add token mới nên cần resize lại model text endcoder
    text_encoder.resize_token_embeddings(len(tokenizer))

    # Sau khi resize:
    print("Sau khi resize:")
    print(text_encoder.get_input_embeddings().weight.shape)
    # torch.Size([49410, 768])
    # exit()

    # Bước 7: Khởi tạo embedding

    if args.initialize_tokens is not None:
        assert len(args.initialize_tokens)==args.n_persudo_tokens,"The number of `initialize_tokens` is not equal to `n_persudo_tokens`"
        initialize_embeds=token_cross_init(args.initialize_tokens,tokenizer,text_encoder)
    else:
        # initialize_embeds=celeb_names_cross_init(args.celeb_path,tokenizer,text_encoder,args.n_persudo_tokens)
        if args.use_fusion:
            # Use the first image as reference
            ref_image = os.path.join(args.train_data_dir, os.listdir(args.train_data_dir)[0])
            initialize_embeds = fusion_cross_init(
                args.celeb_path,
                ref_image,
                tokenizer,
                text_encoder,
                args.n_persudo_tokens,
                args.fusion_weight,
                args.use_attention,
                args.use_transformer
            )
        else:
            initialize_embeds=celeb_names_cross_init(
                args.celeb_path,
                tokenizer,
                text_encoder,
                args.n_persudo_tokens
            )
    print("initialize embeds: (V-init)")
    print(initialize_embeds)
    # exit()
    # lấy embeddings của text_encoder - Gán giá trị initialize_embeds vào các token ID vừa tạo.
    text_encoder.get_input_embeddings().weight.data[placeholder_token_ids]=initialize_embeds
    # text_encoder.get_input_embeddings().weight.data: là nơi chứa embedding cho tất cả token trong CLIP tokenizer.
    initialize_embeds=initialize_embeds.clone().detach().to(accelerator.device)
    # Ngắt kết nối khỏi đồ thị tính toán, chuyển sang thiết bị huấn luyện (GPU)
    # .clone().detach() → ngắt khỏi graph cũ (đề phòng việc bị tính gradient sai).

    # Freeze vae and unet (Đóng băng (freeze) VAE và UNet):  VAE (Autoencoder) & UNet đã được huấn luyện trước và không cần thay đổi.
    vae.requires_grad_(False)
    unet.requires_grad_(False)
    # Freeze all parameters except for the token embeddings in text encoder
    #  Freeze luôn các thành phần trong text_encoder ngoại trừ token_embedding
    # token_embedding là thứ duy nhất bạn muốn huấn luyện, phần còn lại giữ nguyên.
    # Điều này đảm bảo quá trình huấn luyện chỉ thay đổi embedding của pseudo token, không ảnh hưởng mô hình
    text_encoder.text_model.encoder.requires_grad_(False)
    text_encoder.text_model.final_layer_norm.requires_grad_(False)
    text_encoder.text_model.embeddings.position_embedding.requires_grad_(False)

    if args.gradient_checkpointing: #Gradient checkpointing giúp tiết kiệm bộ nhớ GPU khi huấn luyện mô hình lớn. Có thể cân nhắc thêm param này
        # Keep unet in train mode if we are using gradient checkpointing to save memory.
        # The dropout cannot be != 0 so it doesn't matter if we are in eval or train mode.
        unet.train()
        text_encoder.gradient_checkpointing_enable()
        unet.enable_gradient_checkpointing()

    if args.enable_xformers_memory_efficient_attention: #  XFormers giúp tối ưu attention trong Transformer để chạy nhanh hơn, ít tốn RAM hơn .Có thể cân nhắc thêm param này
        if is_xformers_available(): #GPU hỗ trợ, ta bật chế độ này cho Unet.
            import xformers

            xformers_version = version.parse(xformers.__version__)
            if xformers_version == version.parse("0.0.16"):
                logger.warn(
                    "xFormers 0.0.16 cannot be used for training in some GPUs. If you observe problems during training, please update xFormers to at least 0.0.17. See https://huggingface.co/docs/diffusers/main/en/optimization/xformers for more details."
                )
            unet.enable_xformers_memory_efficient_attention()
        else:
            raise ValueError("xformers is not available. Make sure it is installed correctly")

    # Enable TF32 for faster training on Ampere GPUs,
    # cf https://pytorch.org/docs/stable/notes/cuda.html#tensorfloat-32-tf32-on-ampere-devices
    if args.allow_tf32:
        torch.backends.cuda.matmul.allow_tf32 = True

    #cần tìm hiểu rõ
    if args.scale_lr:
        args.learning_rate = ( #learning_rate: Tốc độ học. Learning rate quá nhỏ → mô hình học rất chậm. Learning rate quá lớn → mô hình có thể không hội tụ.
            #train_batch_size: Số lượng mẫu (ảnh, văn bản, dữ liệu,...) mà mô hình xử lý trong một lần cập nhật
            #gradient_accumulation_steps: Tích lũy gradient: Khi GPU không đủ RAM, ta chia batch ra nhiều phần nhỏ rồi cộng dồn lại trước khi cập nhật mô hình.
                #Ví dụ:
                #Nếu batch_size = 32, gradient_accumulation_steps =1. nhưng GPU chỉ chứa được 8 ảnh => loi
                # batch_size = 8
                #Ta có thể chia nhỏ batch thành 4 phần (gradient_accumulation_steps = 4).
                #Sau 4 bước, mô hình mới cập nhật trọng số như batch size 32.
            args.learning_rate * args.gradient_accumulation_steps * args.train_batch_size * accelerator.num_processes #accelerator.num_processes đang dùng 1 GPU nên nó bằng 1
        )

    # Initialize the optimizer
    optimizer = torch.optim.AdamW( #Dùng AdamW – thuật toán tối ưu phổ biến cho mô hình deep learning.
        text_encoder.get_input_embeddings().parameters(),  # only optimize the embeddings ( Chỉ tối ưu embedding mới, không chạm vào các phần khác của mô hình.)
        lr=args.learning_rate,
        betas=(args.adam_beta1, args.adam_beta2),
        weight_decay=args.adam_weight_decay,
        eps=args.adam_epsilon,
    )

    # Initialize CLIP text encoder optimizer if finetuning
    if args.finetune_clip:
        if args.use_knowledge_preservation:
            from models.fusion_modules import CLIPTextEncoderWithKnowledgePreservation
            text_encoder = CLIPTextEncoderWithKnowledgePreservation(
                text_encoder,
                dim=1024,
                finetune_layers=getattr(args, 'finetune_layers', None),
                use_projection=getattr(args, 'use_projection', True)
            )
        else:
            from models.fusion_modules import CLIPTextEncoderFinetune
            text_encoder = CLIPTextEncoderFinetune(text_encoder)

        # Tạo optimizer riêng cho CLIP text encoder
        clip_optimizer = torch.optim.AdamW(
            text_encoder.parameters(),
            lr=args.clip_lr,
            weight_decay=args.clip_weight_decay
        )
        clip_lr_scheduler = get_scheduler(
            args.lr_scheduler,
            optimizer=clip_optimizer,
            num_warmup_steps=args.lr_warmup_steps * args.gradient_accumulation_steps,
            num_training_steps=args.max_train_steps * args.gradient_accumulation_steps,
        )
        clip_optimizer, clip_lr_scheduler = accelerator.prepare(
            clip_optimizer, clip_lr_scheduler
        )

    # Bước 8: Huấn luyện
    # Dataset and DataLoaders creation:
    train_dataset = TextualInversionDataset(
        data_root=args.train_data_dir,
        tokenizer=tokenizer,
        size=args.resolution,
        placeholder_tokens=placeholder_tokens,
        repeats=args.repeats,
        learnable_property=args.learnable_property,
        center_crop=args.center_crop,
        set="train",
    )
    print("train_dataset: ")
    print(train_dataset)
    # exit()
    #train_dataloader: DataLoader giúp tải dữ liệu theo từng batch.
        #DataLoader sẽ gọi __getitem__() mỗi khi cần tạo ra một sample (ảnh + caption)
    train_dataloader = torch.utils.data.DataLoader(
        train_dataset,
        batch_size=args.train_batch_size,
        shuffle=True, #truyền true để train_dataset ngầm chạy với vòng lặp for i từ 0 đến len(train_dataset) (0-99)
        num_workers=args.dataloader_num_workers
    )
    print("train_dataloader: ")
    print(train_dataloader)
    # exit()
    if args.validation_epochs is not None:
        warnings.warn(
            f"FutureWarning: You are doing logging with validation_epochs={args.validation_epochs}."
            " Deprecated validation_epochs in favor of `validation_steps`"
            f"Setting `args.validation_steps` to {args.validation_epochs * len(train_dataset)}",
            FutureWarning,
            stacklevel=2,
        )
        args.validation_steps = args.validation_epochs * len(train_dataset)

    # Scheduler and math around the number of training steps.
    overrode_max_train_steps = False
    #num_update_steps_per_epoch: Số lần cập nhật trọng số trong một epoch.
    print("len(train_dataloader)")
    print(len(train_dataloader)) # 100 / 4 ~ 25
    num_update_steps_per_epoch = math.ceil(len(train_dataloader) / args.gradient_accumulation_steps)
    print("num_update_steps_per_epoch:")
    print(num_update_steps_per_epoch)
    # exit()
    # max_train_steps: Nếu chưa được đặt, nó sẽ được tính dựa trên số epoch và số bước cập nhật.
    if args.max_train_steps is None:
        args.max_train_steps = args.num_train_epochs * num_update_steps_per_epoch
        overrode_max_train_steps = True

    #lr_scheduler: Điều chỉnh learning rate theo từng bước huấn luyện.
    lr_scheduler = get_scheduler(
        args.lr_scheduler,
        optimizer=optimizer,
        num_warmup_steps=args.lr_warmup_steps * args.gradient_accumulation_steps,
        num_training_steps=args.max_train_steps * args.gradient_accumulation_steps,
    )

    # Prepare everything with our `accelerator`.
    text_encoder, optimizer, train_dataloader, lr_scheduler = accelerator.prepare(
        text_encoder, optimizer, train_dataloader, lr_scheduler
    )

    # For mixed precision training we cast the unet and vae weights to half-precision
    # as these models are only used for inference, keeping weights in full precision is not required.
    # mixed_precision (fp16/bf16): Giảm bộ nhớ sử dụng và tăng tốc độ huấn luyện bằng cách dùng số thực 16-bit thay vì 32-bit.
    weight_dtype = torch.float32
    if accelerator.mixed_precision == "fp16":
        weight_dtype = torch.float16
    elif accelerator.mixed_precision == "bf16":
        weight_dtype = torch.bfloat16

    #Chuyển mô hình về thiết bị và cấu hình kiểu dữ liệu
    # Move vae and unet to device and cast to weight_dtype
    unet.to(accelerator.device, dtype=weight_dtype)
    vae.to(accelerator.device, dtype=weight_dtype)

    # We need to recalculate our total training steps as the size of the training dataloader may have changed.
    num_update_steps_per_epoch = math.ceil(len(train_dataloader) / args.gradient_accumulation_steps)
    if overrode_max_train_steps:
        args.max_train_steps = args.num_train_epochs * num_update_steps_per_epoch
    # Afterwards we recalculate our number of training epochs
    args.num_train_epochs = math.ceil(args.max_train_steps / num_update_steps_per_epoch)

    # We need to initialize the trackers we use, and also store our configuration.
    # The trackers initializes automatically on the main process.
    if accelerator.is_main_process:
        accelerator.init_trackers("textual_inversion", config=vars(args))

    # Train!
    total_batch_size = args.train_batch_size * accelerator.num_processes * args.gradient_accumulation_steps

    logger.info("***** Running training *****")
    logger.info(f"  Num examples = {len(train_dataset)}")
    logger.info(f"  Num Epochs = {args.num_train_epochs}") # 5000/17 ~ 295
    logger.info(f"  Instantaneous batch size per device = {args.train_batch_size}")
    logger.info(f"  Total train batch size (w. parallel, distributed & accumulation) = {total_batch_size}")
    logger.info(f"  Gradient Accumulation steps = {args.gradient_accumulation_steps}")
    logger.info(f"  Total optimization steps = {args.max_train_steps}")
    global_step = 0
    first_epoch = 0
    # exit()
    # Potentially load in the weights and states from a previous save
    #Khôi phục trạng thái huấn luyện nếu có checkpoint
    if args.resume_from_checkpoint:
        if args.resume_from_checkpoint != "latest":
            path = os.path.basename(args.resume_from_checkpoint)
        else:
            # Get the most recent checkpoint
            dirs = os.listdir(args.output_dir)
            dirs = [d for d in dirs if d.startswith("checkpoint")]
            dirs = sorted(dirs, key=lambda x: int(x.split("-")[1]))
            path = dirs[-1] if len(dirs) > 0 else None

        if path is None:
            accelerator.print(
                f"Checkpoint '{args.resume_from_checkpoint}' does not exist. Starting a new training run."
            )
            args.resume_from_checkpoint = None
        else:
            accelerator.print(f"Resuming from checkpoint {path}")
            accelerator.load_state(os.path.join(args.output_dir, path))
            global_step = int(path.split("-")[1])

            resume_global_step = global_step * args.gradient_accumulation_steps
            first_epoch = global_step // num_update_steps_per_epoch
            resume_step = resume_global_step % (num_update_steps_per_epoch * args.gradient_accumulation_steps)

    # Only show the progress bar once on each machine.
    progress_bar = tqdm(range(global_step, args.max_train_steps), disable=not accelerator.is_local_main_process)
    progress_bar.set_description("Steps")

    # exit()
    #Lưu lại embedding ban đầu để tránh cập nhật toàn bộ từ vựng
    # keep original embeddings as reference
    orig_embeds_params = accelerator.unwrap_model(text_encoder).get_input_embeddings().weight.data.clone()

    logger.info(args.num_train_epochs)
    # exit()
    for epoch in range(first_epoch, args.num_train_epochs):
        # Đặt mô hình text_encoder vào chế độ huấn luyện
        text_encoder.train()
        # Vòng lặp này đi qua tất cả các batch trong train_dataloader
        for step, batch in enumerate(train_dataloader):
            # Skip steps until we reach the resumed step
            #Skip các bước đã huấn luyện trước đó (nếu tiếp tục từ checkpoint)
            if args.resume_from_checkpoint and epoch == first_epoch and step < resume_step:
                if step % args.gradient_accumulation_steps == 0:
                    progress_bar.update(1)
                continue

            #Forward diffusion (quá trình thêm nhiễu)
            with accelerator.accumulate(text_encoder):
                # Convert images to latent space
                #➡️ VAE được dùng để mã hóa ảnh thành dạng nhẹ hơn gọi là latent vector (không gian nén) (khoảng 64x64x4).
                latents = vae.encode(batch["pixel_values"].to(dtype=weight_dtype)).latent_dist.sample().detach()
                #latent_dist.sample().detach(): Lấy mẫu từ phân phối latent
                # và ngắt liên kết với đồ thị tính toán (detach), (vì VAE không cần huấn luyện ở đây (nó chỉ encode ảnh một lần).)
                # đảm bảo rằng VAE không cập nhật trong quá trình này.
                latents = latents * vae.config.scaling_factor #* scaling_factor: nhân thêm hệ số để đưa latent về đúng scale cho diffusion model.

                # Sample noise that we'll add to the latents
                # Tạo nhiễu và thêm nhiễu (forward diffusion process)
                # Stable Diffusion học bằng cách thêm nhiễu vào latent ảnh, rồi học cách khử nhiễu đó (reverse diffusion).
                #Tạo một tensor nhiễu Gaussian (noise) có cùng kích thước với latents.
                noise = torch.randn_like(latents)
                bsz = latents.shape[0]
                # Sample a random timestep for each image
                # num_train_timesteps=noise_scheduler.config.num_train_timesteps
                # Chọn ngẫu nhiên một timestep trong khoảng [0, num_train_timesteps] cho mỗi ảnh.
                timesteps = torch.randint(0, noise_scheduler.config.num_train_timesteps, (bsz,), device=latents.device)
                timesteps = timesteps.long()

                # Add noise to the latents according to the noise magnitude at each timestep
                # (this is the forward diffusion process)
                #Thêm nhiễu vào latents theo độ lớn nhiễu của từng timesteps.
                # Đây là bước trong quá trình forward diffusion, mô phỏng quá trình thêm nhiễu vào ảnh gốc.
                    # Timestep nhỏ → ít noise.
                    # Timestep lớn → gần như noise trắng. (bị che gần hết bởi noise)
                noisy_latents = noise_scheduler.add_noise(latents, noise, timesteps)

                # Get the text embedding for conditioning
                # Chuyển input văn bản thành embedding: Lấy embedding của các câu lệnh văn bản từ text_encoder (ben dataset)
                # encoder_hidden_states = text_encoder(batch["input_ids"])[0].to(dtype=weight_dtype)
                # Nếu đang finetune CLIP, text_encoder là CLIPTextEncoderFinetune
                # Lớp này có thể nhận input_ids hoặc inputs_embeds
                if args.finetune_clip:
                    # text_encoder(batch["input_ids"]) sẽ trả về đối tượng CLIPTextEncoderOutput
                    # cần lấy thuộc tính last_hidden_state
                    encoder_hidden_states_output = text_encoder(
                        input_ids=batch["input_ids"],
                        attention_mask=batch["attention_mask"] # Thêm attention_mask để xử lý padding
                    )
                    encoder_hidden_states = encoder_hidden_states_output.last_hidden_state.to(dtype=weight_dtype)
                else:
                    # Nếu không finetune, text_encoder là CLIPTextModel gốc
                    # Nó trả về một tuple, cần lấy phần tử đầu tiên [0]
                    encoder_hidden_states = text_encoder(
                        batch["input_ids"],
                        attention_mask=batch["attention_mask"] # Thêm attention_mask
                    )[0].to(dtype=weight_dtype)

                # Predict the noise residual
                # Dự đoán nhiễu từ mô hình UNet: unet sẽ dự đoán nhiễu residual từ các latent vector đã thêm nhiễu,
                # sử dụng các embedding văn bản làm điều kiện.
                # Nó học cách: "từ ảnh nhiễu, biết thời điểm thêm nhiễu + prompt, đoán lại được nhiễu ban đầu."
                model_pred = unet(noisy_latents, timesteps, encoder_hidden_states).sample

                # Get the target for loss depending on the prediction type
                if noise_scheduler.config.prediction_type == "epsilon":
                    target = noise
                elif noise_scheduler.config.prediction_type == "v_prediction":
                    target = noise_scheduler.get_velocity(latents, noise, timesteps)
                else:
                    raise ValueError(f"Unknown prediction type {noise_scheduler.config.prediction_type}")

                # Enhanced Loss Computation with Three Components:
                # Total Loss = α×Diffusion Loss + γ×Regularization Loss + β×Knowledge Preservation Loss

                # 1. Diffusion Loss (α) - Main loss for noise prediction
                diffusion_loss = F.mse_loss(model_pred, target, reduction='mean')

                # 2. Regularization Loss (γ) - Keep embeddings close to initialization
                token_embeds = accelerator.unwrap_model(text_encoder).get_input_embeddings().weight
                reg_loss = F.pairwise_distance(
                    token_embeds[placeholder_token_ids],
                    initialize_embeds,
                    p=2
                ).mean()

                # 3. Knowledge Preservation Loss (β) - Prevent semantic drift
                knowledge_preservation_loss = torch.tensor(0.0, device=accelerator.device)

                if args.finetune_clip and args.use_knowledge_preservation:
                    # Get text embeddings with knowledge preservation loss computation
                    text_outputs = text_encoder(
                        batch["input_ids"],
                        attention_mask=batch["attention_mask"],
                        return_knowledge_preservation_loss=True
                    )

                    if hasattr(text_outputs, 'knowledge_preservation_loss'):
                        knowledge_preservation_loss = text_outputs.knowledge_preservation_loss

                # Combine all loss components with configurable weights
                diffusion_weight = getattr(args, 'diffusion_weight', 1.0)
                reg_weight = getattr(args, 'reg_weight', 1e-5)
                knowledge_preservation_weight = getattr(args, 'knowledge_preservation_weight', 0.1)

                total_loss = (
                    diffusion_weight * diffusion_loss +
                    reg_weight * reg_loss +
                    knowledge_preservation_weight * knowledge_preservation_loss
                )

                # Legacy CLIP text loss (for backward compatibility)
                if args.finetune_clip and not args.use_knowledge_preservation:
                    # Original fixed anchor approach
                    text_outputs = text_encoder(
                        batch["input_ids"],
                        attention_mask=batch["attention_mask"]
                    )

                    target_text = "a person's face"
                    target_ids = tokenizer(
                        target_text,
                        padding="max_length",
                        max_length=tokenizer.model_max_length,
                        truncation=True,
                        return_tensors="pt",
                    ).input_ids.to(accelerator.device)

                    target_outputs = text_encoder(
                        input_ids=target_ids,
                        attention_mask=torch.ones_like(target_ids)
                    )

                    text_loss = 1 - F.cosine_similarity(
                        text_outputs.last_hidden_state.mean(dim=1),
                        target_outputs.last_hidden_state.mean(dim=1)
                    ).mean()

                    total_loss = total_loss + args.text_loss_weight * text_loss

                loss = total_loss

                accelerator.backward(loss) #Là hàm lan truyền ngược (backpropagation) để tính gradient của các trọng số dựa trên loss.

                optimizer.step() #Thực hiện cập nhật các trọng số của mô hình
                lr_scheduler.step() #Điều chỉnh learning rate theo scheduler.
                optimizer.zero_grad() #làm sạch gradient sau mỗi bước huấn luyện.

                # Update CLIP text encoder nếu finetune
                if args.finetune_clip:
                    clip_optimizer.step()
                    clip_lr_scheduler.step()
                    clip_optimizer.zero_grad()

                # Let's make sure we don't update any embedding weights besides the newly added token
                # Chỉ cập nhật embedding của các token placeholder (<thuyduong>), không thay đổi từ vựng gốc.
                # Cập nhật lại embedding ban đầu cho các token khác để tránh thay đổi vô ý.
                vocab = torch.arange(len(tokenizer)) #→ Tạo tensor như [0, 1, 2, ..., vocab_size-1] đại diện cho tất cả token ID trong mô hình.
                index_no_updates = (vocab != placeholder_token_ids[0]) #đánh dấu tất cả token ID không phải là <thuyduong>
                for token_id in placeholder_token_ids[1:]:
                    index_no_updates=torch.logical_and(index_no_updates,vocab != token_id)
                # → Đây là bước khôi phục embedding ban đầu (orig_embeds_params) cho toàn bộ token không phải là <thuyduong>.
                # => Đảm bảo rằng sau mỗi bước update, chỉ <thuyduong> được học, phần còn lại không thay đổi.
                with torch.no_grad():
                    token_embeds[index_no_updates] = orig_embeds_params[index_no_updates]

            # Checks if the accelerator has performed an optimization step behind the scenes
            if accelerator.sync_gradients:
                progress_bar.update(1)
                global_step += 1
                # Cứ sau save_steps bước, lưu embedding đã học (learned_embeds.bin).
                if global_step % args.save_steps == 0:
                    save_path = os.path.join(args.output_dir, f"learned_embeds-steps-{global_step}.bin")
                    save_progress(text_encoder, placeholder_tokens, placeholder_token_ids, accelerator, args, save_path)

                if accelerator.is_main_process:
                    if global_step % args.checkpointing_steps == 0:
                        save_path = os.path.join(args.output_dir, f"checkpoint-{global_step}")
                        accelerator.save_state(save_path)
                        logger.info(f"Saved state to {save_path}")

                    if args.validation_prompt is not None and global_step % args.validation_steps == 0:
                        log_validation(text_encoder, tokenizer, unet, vae, args, accelerator, weight_dtype, global_step, placeholder_tokens)

            # Enhanced logging with detailed loss breakdown
            logs = {
                "total_loss": loss.detach().item(),
                "diffusion_loss": diffusion_loss.detach().item(),
                "regularization_loss": reg_loss.detach().item(),
                "knowledge_preservation_loss": knowledge_preservation_loss.detach().item(),
                "lr": lr_scheduler.get_last_lr()[0],
            }

            # Add CLIP optimizer learning rate if available
            if args.finetune_clip:
                logs["clip_lr"] = clip_lr_scheduler.get_last_lr()[0]

            progress_bar.set_postfix(**logs)
            accelerator.log(logs, step=global_step)

            if global_step >= args.max_train_steps:
                break
    # Create the pipeline using using the trained modules and save it.
    # Đợi tất cả các tiến trình hoàn thành trước khi lưu mô hình
    accelerator.wait_for_everyone()
    if accelerator.is_main_process:
        if args.push_to_hub and args.only_save_embeds:
            logger.warn("Enabling full model saving because --push_to_hub=True was specified.")
            save_full_model = True
        else:
            save_full_model = not args.only_save_embeds #chỉ lưu toàn bộ mô hình khi args.only_save_embeds = False
        if save_full_model:
            pipeline = StableDiffusionPipeline.from_pretrained(
                args.pretrained_model_name_or_path,
                text_encoder=accelerator.unwrap_model(text_encoder),
                vae=vae,
                unet=unet,
                tokenizer=tokenizer,
            )
            pipeline.save_pretrained(args.output_dir)
        # Save the newly trained embeddings
        save_path = os.path.join(args.output_dir, "learned_embeds.bin")
        save_progress(text_encoder, placeholder_tokens, placeholder_token_ids, accelerator, args, save_path)

        if args.push_to_hub:
            repo.push_to_hub(commit_message="End of training", blocking=False, auto_lfs_prune=True)

    accelerator.end_training()

#Duong -- Đọc các tham số
def parse_args():
    parser = argparse.ArgumentParser(description="Simple example of a training script.")
    parser.add_argument(
        "--save_steps", # Mỗi 500 bước sẽ lưu một checkpoint.
        type=int,
        default=500,
        help="Save learned_embeds.bin every X updates steps.",
    )
    parser.add_argument(
        "--only_save_embeds", # Nếu có flag này, chỉ lưu embeddings.
        action="store_true",
        default=False,
        help="Save only the embeddings for the new concept.",
    )
    parser.add_argument(
        "--pretrained_model_name_or_path", # Đường dẫn mô hình pretrained.
        type=str,
        default=None,
        required=True,
        help="Path to pretrained model or model identifier from huggingface.co/models.",
    )
    parser.add_argument(
        "--revision", # Phiên bản mô hình tải từ Hugging Face.
        type=str,
        default=None,
        required=False,
        help="Revision of pretrained model identifier from huggingface.co/models.",
    )
    parser.add_argument(
        "--tokenizer_name", # Nếu tokenizer khác với model, chỉ định ở đây.
        type=str,
        default=None,
        help="Pretrained tokenizer name or path if not the same as model_name",
    )
    parser.add_argument(
        "--train_data_dir", type=str, default=None, required=True, help="A folder containing the training data."
    )
    parser.add_argument(
        "--placeholder_token",
        type=str,
        default=None,
        required=True,
        help="A token to use as a placeholder for the concept.",
    )
    parser.add_argument(
        "--initialize_tokens",
        type=str,
        default=None,
        required=False,
        nargs="*",
        help="Tokens to use as initializer words."
    )

    parser.add_argument(
        "--celeb_path",
        type=str,
        default=None,
        required=False,
        help="Celeb basis file that contains celeb names."
    )

    parser.add_argument(
        "--n_persudo_tokens",
        type=int,
        default=2,
        required=True,
        help="Number of persudo tokens to use in training.",
    )

    parser.add_argument(
        "--reg_weight",
        type=float,
        default=1e-5,
        required=False,
        help="Weight of the regularization term.",
    )

    parser.add_argument("--learnable_property", type=str, default="object", help="Choose between 'object' and 'style'")
    parser.add_argument("--repeats", type=int, default=100, help="How many times to repeat the training data.")
    parser.add_argument(
        "--output_dir",
        type=str,
        default="text-inversion-model",
        help="The output directory where the model predictions and checkpoints will be written.",
    )
    parser.add_argument("--seed", type=int, default=None, help="A seed for reproducible training.")
    parser.add_argument(
        "--resolution",
        type=int,
        default=512,
        help=(
            "The resolution for input images, all the images in the train/validation dataset will be resized to this"
            " resolution"
        ),
    )
    parser.add_argument(
        "--center_crop", action="store_true", help="Whether to center crop images before resizing to resolution."
    )
    parser.add_argument(
        "--train_batch_size", type=int, default=8, help="Batch size (per device) for the training dataloader."
    )
    parser.add_argument("--num_train_epochs", type=int, default=100)
    parser.add_argument(
        "--max_train_steps",
        type=int,
        default=5000,
        help="Total number of training steps to perform.  If provided, overrides num_train_epochs.",
    )
    parser.add_argument(
        "--gradient_accumulation_steps",
        type=int,
        default=1,
        help="Number of updates steps to accumulate before performing a backward/update pass.",
    )
    parser.add_argument(
        "--gradient_checkpointing",
        action="store_true",
        help="Whether or not to use gradient checkpointing to save memory at the expense of slower backward pass.",
    )
    parser.add_argument(
        "--learning_rate",
        type=float,
        default=1e-4,
        help="Initial learning rate (after the potential warmup period) to use.",
    )
    parser.add_argument(
        "--scale_lr",
        action="store_true",
        default=False,
        help="Scale the learning rate by the number of GPUs, gradient accumulation steps, and batch size.",
    )
    parser.add_argument(
        "--lr_scheduler",
        type=str,
        default="constant",
        help=(
            'The scheduler type to use. Choose between ["linear", "cosine", "cosine_with_restarts", "polynomial",'
            ' "constant", "constant_with_warmup"]'
        ),
    )
    parser.add_argument(
        "--lr_warmup_steps", type=int, default=500, help="Number of steps for the warmup in the lr scheduler."
    )
    parser.add_argument(
        "--dataloader_num_workers",
        type=int,
        default=0,
        help=(
            "Number of subprocesses to use for data loading. 0 means that the data will be loaded in the main process."
        ),
    )
    parser.add_argument("--adam_beta1", type=float, default=0.9, help="The beta1 parameter for the Adam optimizer.")
    parser.add_argument("--adam_beta2", type=float, default=0.999, help="The beta2 parameter for the Adam optimizer.")
    parser.add_argument("--adam_weight_decay", type=float, default=1e-2, help="Weight decay to use.")
    parser.add_argument("--adam_epsilon", type=float, default=1e-08, help="Epsilon value for the Adam optimizer")
    parser.add_argument("--push_to_hub", action="store_true", help="Whether or not to push the model to the Hub.")
    parser.add_argument("--hub_token", type=str, default=None, help="The token to use to push to the Model Hub.")
    parser.add_argument(
        "--hub_model_id",
        type=str,
        default=None,
        help="The name of the repository to keep in sync with the local `output_dir`.",
    )
    parser.add_argument(
        "--logging_dir",
        type=str,
        default="logs",
        help=(
            "[TensorBoard](https://www.tensorflow.org/tensorboard) log directory. Will default to"
            " *output_dir/runs/**CURRENT_DATETIME_HOSTNAME***."
        ),
    )
    parser.add_argument(
        "--mixed_precision",
        type=str,
        default="no",
        choices=["no", "fp16", "bf16"],
        help=(
            "Whether to use mixed precision. Choose"
            "between fp16 and bf16 (bfloat16). Bf16 requires PyTorch >= 1.10."
            "and an Nvidia Ampere GPU."
        ),
    )
    parser.add_argument(
        "--allow_tf32",
        action="store_true",
        help=(
            "Whether or not to allow TF32 on Ampere GPUs. Can be used to speed up training. For more information, see"
            " https://pytorch.org/docs/stable/notes/cuda.html#tensorfloat-32-tf32-on-ampere-devices"
        ),
    )
    parser.add_argument(
        "--report_to",
        type=str,
        default="tensorboard",
        help=(
            'The integration to report the results and logs to. Supported platforms are `"tensorboard"`'
            ' (default), `"wandb"` and `"comet_ml"`. Use `"all"` to report to all integrations.'
        ),
    )
    parser.add_argument(
        "--validation_prompt",
        type=str,
        default=None,
        help="A prompt that is used during validation to verify that the model is learning.",
    )
    parser.add_argument(
        "--validation_prompt_file",
        type=str,
        default=None,
        help="A file containing several prompts that are used during validation to verify that the model is learning.",
    )
    parser.add_argument(
        "--num_validation_images",
        type=int,
        default=4,
        help="Number of images that should be generated during validation with `validation_prompt`.",
    )
    parser.add_argument(
        "--validation_steps",
        type=int,
        default=100,
        help=(
            "Run validation every X steps. Validation consists of running the prompt"
            " `args.validation_prompt` multiple times: `args.num_validation_images`"
            " and logging the images."
        ),
    )
    parser.add_argument(
        "--validation_epochs",
        type=int,
        default=None,
        help=(
            "Deprecated in favor of validation_steps. Run validation every X epochs. Validation consists of running the prompt"
            " `args.validation_prompt` multiple times: `args.num_validation_images`"
            " and logging the images."
        ),
    )
    parser.add_argument("--local_rank", type=int, default=-1, help="For distributed training: local_rank")
    parser.add_argument(
        "--checkpointing_steps",
        type=int,
        default=500,
        help=(
            "Save a checkpoint of the training state every X updates. These checkpoints are only suitable for resuming"
            " training using `--resume_from_checkpoint`."
        ),
    )
    parser.add_argument(
        "--checkpoints_total_limit",
        type=int,
        default=None,
        help=(
            "Max number of checkpoints to store. Passed as `total_limit` to the `Accelerator` `ProjectConfiguration`."
            " See Accelerator::save_state https://huggingface.co/docs/accelerate/package_reference/accelerator#accelerate.Accelerator.save_state"
            " for more docs"
        ),
    )
    parser.add_argument(
        "--resume_from_checkpoint",
        type=str,
        default=None,
        help=(
            "Whether training should be resumed from a previous checkpoint. Use a path saved by"
            ' `--checkpointing_steps`, or `"latest"` to automatically select the last available checkpoint.'
        ),
    )
    parser.add_argument(
        "--enable_xformers_memory_efficient_attention", action="store_true", help="Whether or not to use xformers."
    )

    # Fusion arguments
    parser.add_argument(
        "--use_fusion",
        action="store_true",
        help="Whether to use image-text fusion initialization"
    )
    parser.add_argument(
        "--fusion_weight",
        type=float,
        default=0.5,
        help="Weight for text-image feature fusion (0-1)"
    )
    parser.add_argument(
        "--use_attention",
        action="store_true",
        default=False,
        help="Whether to use attention mechanism in fusion"
    )
    parser.add_argument(
        "--use_transformer",
        action="store_true",
        default=False,
        help="Whether to use transformer encoder in fusion"
    )

    # Finetune arguments
    parser.add_argument(
        "--finetune_clip",
        action="store_true",
        help="Whether to finetune CLIP text encoder"
    )
    parser.add_argument(
        "--clip_lr",
        type=float,
        default=1e-5,
        help="Learning rate for CLIP text encoder finetuning"
    )
    parser.add_argument(
        "--clip_weight_decay",
        type=float,
        default=0.01,
        help="Weight decay for CLIP text encoder finetuning"
    )
    parser.add_argument(
        "--text_loss_weight",
        type=float,
        default=0.1,
        help="Weight for CLIP text loss"
    )

    # Enhanced Knowledge Preservation Arguments
    parser.add_argument(
        "--use_knowledge_preservation",
        action="store_true",
        help="Whether to use knowledge preservation loss instead of fixed anchor"
    )
    parser.add_argument(
        "--knowledge_preservation_weight",
        type=float,
        default=0.05,  # Reduced default for better identity preservation
        help="Weight for knowledge preservation loss (β in the paper). Range: 0.01-0.2"
    )
    parser.add_argument(
        "--diffusion_weight",
        type=float,
        default=1.0,
        help="Weight for diffusion loss (α in the paper)"
    )
    parser.add_argument(
        "--finetune_layers",
        type=str,
        nargs="*",
        default=None,
        help="Specific layers to fine-tune (e.g., text_model.encoder.layers.11) or 'all' for all layers"
    )
    parser.add_argument(
        "--use_projection",
        action="store_true",
        default=True,
        help="Whether to use projection layers in CLIP fine-tuning"
    )

    args = parser.parse_args()
    env_local_rank = int(os.environ.get("LOCAL_RANK", -1))
    if env_local_rank != -1 and env_local_rank != args.local_rank:
        args.local_rank = env_local_rank

    if args.train_data_dir is None:
        raise ValueError("You must specify a train data directory.")

    if args.validation_prompt is not None and args.validation_prompt_file is not None:
        raise ValueError("`--validation_prompt` cannot be used with `--validation_prompt_file`")

    if args.validation_prompt is not None:
        args.validation_prompt=[args.validation_prompt]

    if args.validation_prompt_file is not None:
        with open(args.validation_prompt_file,'r') as f:
            args.validation_prompt=f.read().splitlines()

    if args.initialize_tokens is not None and args.celeb_path is not None:
        raise ValueError("`--initialize_tokens` cannot be used with `--celeb_path`")

    if args.initialize_tokens is None and args.celeb_path is None:
        raise ValueError("`--initialize_tokens` and `--celeb_path` cannot both be empty.")

    return args

if __name__ == "__main__":
    train()