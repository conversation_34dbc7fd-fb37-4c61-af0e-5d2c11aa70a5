python train_cross_init.py \
    --save_steps 1000 \
    --only_save_embeds \
    --placeholder_token "<6525>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 3000 \
    --train_data_dir "./examples/input_images/GS_300dpi" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day060525_3000_NoFusion_NoRefine_Prof_LR000625_Rw1e5/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4
-------------- test with train step = 1000 ------------------
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day060525_3000_NoFusion_NoRefine_Prof_LR000625_Rw1e5/learned_embeddings/learned_embeds-steps-1000.bin" \
    --prompt "a photo of {} person" \
    --save_dir "./logs/IMAGES/Day060525_3000_NoFusion_NoRefine_Prof_LR000625_Rw1e5/TrainStep1000_TestStep50" \
    --num_images_per_prompt=15 \
    --n_iter=10 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day060525_3000_NoFusion_NoRefine_Prof_LR000625_Rw1e5/learned_embeddings/learned_embeds-steps-1000.bin" \
    --prompt "a {} person is eating his birthday cake" \
    --save_dir "./logs/IMAGES/Day060525_3000_NoFusion_NoRefine_Prof_LR000625_Rw1e5/TrainStep1000_TestStep50" \
    --num_images_per_prompt=15 \
    --n_iter=10 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day060525_3000_NoFusion_NoRefine_Prof_LR000625_Rw1e5/learned_embeddings/learned_embeds-steps-1000.bin" \
    --prompt "a {} person with angry expression" \
    --save_dir "./logs/IMAGES/Day060525_3000_NoFusion_NoRefine_Prof_LR000625_Rw1e5/TrainStep1000_TestStep50" \
    --num_images_per_prompt=15 \
    --n_iter=10 \
    --seed=42

python -m pytorch_fid examples/input_images/GS_300dpi_x150img logs/IMAGES/Day060525_3000_NoFusion_NoRefine_Prof_LR000625_Rw1e5/TrainStep1000_TestStep50/a_{}_person_with_angry_expression
#FID Score: 261.9

python -m pytorch_fid examples/input_images/GS_300dpi_x150img logs/IMAGES/Day060525_3000_NoFusion_NoRefine_Prof_LR000625_Rw1e5/TrainStep1000_TestStep50/a_{}_person_is_eating_his_birthday_cake
#FID Score: 325.1

-------------- test with train step = 2000 ------------------
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day060525_3000_NoFusion_NoRefine_Prof_LR000625_Rw1e5/learned_embeddings/learned_embeds-steps-2000.bin" \
    --prompt "a photo of {} person" \
    --save_dir "./logs/IMAGES/Day060525_3000_NoFusion_NoRefine_Prof_LR000625_Rw1e5/TrainStep2000_TestStep50" \
    --num_images_per_prompt=15 \
    --n_iter=10 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day060525_3000_NoFusion_NoRefine_Prof_LR000625_Rw1e5/learned_embeddings/learned_embeds-steps-2000.bin" \
    --prompt "a {} person is eating his birthday cake" \
    --save_dir "./logs/IMAGES/Day060525_3000_NoFusion_NoRefine_Prof_LR000625_Rw1e5/TrainStep2000_TestStep50" \
    --num_images_per_prompt=15 \
    --n_iter=10 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day060525_3000_NoFusion_NoRefine_Prof_LR000625_Rw1e5/learned_embeddings/learned_embeds-steps-2000.bin" \
    --prompt "a {} person with angry expression" \
    --save_dir "./logs/IMAGES/Day060525_3000_NoFusion_NoRefine_Prof_LR000625_Rw1e5/TrainStep2000_TestStep50" \
    --num_images_per_prompt=15 \
    --n_iter=10 \
    --seed=42











----------- Use fusion -------------------
python train_cross_init.py \
    --save_steps 1000 \
    --only_save_embeds \
    --placeholder_token "<6525>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 3000 \
    --train_data_dir "./examples/input_images/GS_300dpi" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day060525_3000_HaveFusion_NoRefine_Prof_LR000625_Rw1e5/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --use_fusion \
    --fusion_weight 0.8

