-- Fixed true/false fusion
python train_cross_init.py \
    --save_steps 600 \
    --only_save_embeds \
    --placeholder_token "<23525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day230525_500_HaveFusion_TransformerFW08_NoFinetuneCLIP_Celeb3/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --use_fusion \
    --fusion_weight 0.8 \
    --use_transformer

-- wip
python train_cross_init.py \
    --save_steps 600 \
    --only_save_embeds \
    --placeholder_token "<23525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day230525_500_HaveFusion_AttentionFW08_NoFinetuneCLIP_Celeb3/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --use_fusion \
    --fusion_weight 0.8 \
    --use_attention
--chưa
python train_cross_init.py \
    --save_steps 600 \
    --only_save_embeds \
    --placeholder_token "<23525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day230525_500_HaveFusion_Use2_FW08_NoFinetuneCLIP_Celeb3/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --use_fusion \
    --fusion_weight 0.8 \
    --use_attention \
    --use_transformer

--chưa -- transformer
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day230525_500_HaveFusion_TransformerFW08_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a photo of {} person" \
    --save_dir "./logs/IMAGES/Day230525_500_HaveFusion_TransformerFW08_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=5 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day230525_500_HaveFusion_TransformerFW08_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person is eating her birthday cake" \
    --save_dir "./logs/IMAGES/Day230525_500_HaveFusion_TransformerFW08_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=5 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day230525_500_HaveFusion_TransformerFW08_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person with angry expression" \
    --save_dir "./logs/IMAGES/Day230525_500_HaveFusion_TransformerFW08_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=5 \
    --seed=42

--chưa -- attention
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day230525_500_HaveFusion_AttentionFW08_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a photo of {} person" \
    --save_dir "./logs/IMAGES/Day230525_500_HaveFusion_AttentionFW08_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=5 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day230525_500_HaveFusion_AttentionFW08_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person is eating her birthday cake" \
    --save_dir "./logs/IMAGES/Day230525_500_HaveFusion_AttentionFW08_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=5 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day230525_500_HaveFusion_AttentionFW08_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person with angry expression" \
    --save_dir "./logs/IMAGES/Day230525_500_HaveFusion_AttentionFW08_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=5 \
    --seed=42













python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day220525_500_NoFusion_HaveFinetuneCLIP_TLW1e5_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person with angry expression" \
    --save_dir "./logs/IMAGES/Day220525_500_NoFusion_HaveFinetuneCLIP_TLW1e5_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day220525_500_NoFusion_HaveFinetuneCLIP_TLW1e5_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a photo of {} person" \
    --save_dir "./logs/IMAGES/Day220525_500_NoFusion_HaveFinetuneCLIP_TLW1e5_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42
    Day220525_500_NoFusion_HaveFinetuneCLIP_TLW1e5_Celeb3