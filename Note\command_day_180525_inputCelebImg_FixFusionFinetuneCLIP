--------------- train with 500 steps - celeb3--------------------------
python train_cross_init.py \
    --save_steps 600 \
    --only_save_embeds \
    --placeholder_token "<18525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day180525_500_HaveFusionAttentionAndTransformer_HaveFinetuneCLIP_Celeb3/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --use_fusion \
    --fusion_weight 0.8 \
    --use_attention \
    --use_transformer \
    --finetune_clip \
    --clip_lr 1e-5 \
    --clip_weight_decay 0.01 \
    --text_loss_weight 0.1


python train_cross_init.py \
    --save_steps 600 \
    --only_save_embeds \
    --placeholder_token "<18525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day180525_500_HaveFusionAttentionAndTransformer_NoFinetuneCLIP_Celeb3/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --use_fusion \
    --fusion_weight 0.8 \
    --use_attention \
    --use_transformer \

python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day180525_500_HaveFusionAttentionAndTransformer_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a photo of {} person" \
    --save_dir "./logs/IMAGES/Day180525_500_HaveFusionAttentionAndTransformer_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=5 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day180525_500_HaveFusionAttentionAndTransformer_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person is eating his birthday cake" \
    --save_dir "./logs/IMAGES/Day180525_500_HaveFusionAttentionAndTransformer_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=5 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day180525_500_HaveFusionAttentionAndTransformer_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person with angry expression" \
    --save_dir "./logs/IMAGES/Day180525_500_HaveFusionAttentionAndTransformer_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=5 \
    --seed=42


python train_cross_init.py \
    --save_steps 600 \
    --only_save_embeds \
    --placeholder_token "<18525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day180525_500_HaveFusionOnlyTransformer_NoFinetuneCLIP_Celeb3/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --use_fusion \
    --fusion_weight 0.8 \
    --use_transformer \
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day180525_500_HaveFusionOnlyTransformer_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a photo of {} person" \
    --save_dir "./logs/IMAGES/Day180525_500_HaveFusionOnlyTransformer_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=5 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day180525_500_HaveFusionOnlyTransformer_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person is eating her birthday cake" \
    --save_dir "./logs/IMAGES/Day180525_500_HaveFusionOnlyTransformer_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=5 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day180525_500_HaveFusionOnlyTransformer_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person with angry expression" \
    --save_dir "./logs/IMAGES/Day180525_500_HaveFusionOnlyTransformer_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=5 \
    --seed=42


python train_cross_init.py \
    --save_steps 600 \
    --only_save_embeds \
    --placeholder_token "<18525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day180525_500_HaveFusionOnlyTransformerWeight1_NoFinetuneCLIP_Celeb3/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --use_fusion \
    --fusion_weight 1 \
    --use_transformer \
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day180525_500_HaveFusionOnlyTransformerWeight1_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a photo of {} person" \
    --save_dir "./logs/IMAGES/Day180525_500_HaveFusionOnlyTransformerWeight1_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=5 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day180525_500_HaveFusionOnlyTransformerWeight1_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person is eating her birthday cake" \
    --save_dir "./logs/IMAGES/Day180525_500_HaveFusionOnlyTransformerWeight1_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=5 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day180525_500_HaveFusionOnlyTransformerWeight1_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person with angry expression" \
    --save_dir "./logs/IMAGES/Day180525_500_HaveFusionOnlyTransformerWeight1_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=5 \
    --seed=42




python train_cross_init.py \
    --save_steps 1100 \
    --only_save_embeds \
    --placeholder_token "<18525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 1000 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day180525_1000_HaveFusionOnlyTransformer_NoFinetuneCLIP_Celeb3/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --use_fusion \
    --fusion_weight 0.8 \
    --use_transformer \
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day180525_1000_HaveFusionOnlyTransformer_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a photo of {} person" \
    --save_dir "./logs/IMAGES/Day180525_1000_HaveFusionOnlyTransformer_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day180525_1000_HaveFusionOnlyTransformer_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person is eating her birthday cake" \
    --save_dir "./logs/IMAGES/Day180525_1000_HaveFusionOnlyTransformer_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day180525_1000_HaveFusionOnlyTransformer_NoFinetuneCLIP_Celeb3/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person with angry expression" \
    --save_dir "./logs/IMAGES/Day180525_1000_HaveFusionOnlyTransformer_NoFinetuneCLIP_Celeb3/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42
