--------------- train with 500 steps - celeb3--------------------------
python train_cross_init.py \
    --save_steps 320 \
    --only_save_embeds \
    --placeholder_token "<7525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day070525_500_NoFusion_NoRefine_Celeb3_LR000625_Rw1e5/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4
-------------- test with train step = 320 ------------------
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day070525_500_NoFusion_NoRefine_Celeb3_LR000625_Rw1e5/learned_embeddings/learned_embeds-steps-320.bin" \
    --prompt "a photo of {} person" \
    --save_dir "./logs/IMAGES/Day070525_500_NoFusion_NoRefine_Celeb3_LR000625_Rw1e5/TrainStep320_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day070525_500_NoFusion_NoRefine_Celeb3_LR000625_Rw1e5/learned_embeddings/learned_embeds-steps-320.bin" \
    --prompt "a {} person is eating his birthday cake" \
    --save_dir "./logs/IMAGES/Day070525_500_NoFusion_NoRefine_Celeb3_LR000625_Rw1e5/TrainStep320_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day070525_500_NoFusion_NoRefine_Celeb3_LR000625_Rw1e5/learned_embeddings/learned_embeds-steps-320.bin" \
    --prompt "a {} person with angry expression" \
    --save_dir "./logs/IMAGES/Day070525_500_NoFusion_NoRefine_Celeb3_LR000625_Rw1e5/TrainStep320_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42






-------------------- Use fusion -------------------
python train_cross_init.py \
    --save_steps 320 \
    --only_save_embeds \
    --placeholder_token "<7525@c3>" \
    --train_batch_size 2 \
    --scale_lr \
    --n_persudo_tokens 2 \
    --reg_weight "1e-5" \
    --learning_rate 0.000625 \
    --max_train_step 500 \
    --train_data_dir "./examples/input_images/celeb3" \
    --celeb_path "./examples/wiki_names_v2.txt" \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --output_dir "./logs/Day070525_500_HaveFusion_NoRefine_Celeb3_EditedFusion_LR000625_Rw1e5/learned_embeddings" \
    --resolution 512 \
    --gradient_checkpointing \
    --gradient_accumulation_steps 4 \
    --use_fusion \
    --fusion_weight 0.8
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day070525_500_HaveFusion_NoRefine_Celeb3_EditedFusion_LR000625_Rw1e5/learned_embeddings/learned_embeds.bin" \
    --prompt "a photo of {} person" \
    --save_dir "./logs/IMAGES/Day070525_500_HaveFusion_NoRefine_Celeb3_EditedFusion_LR000625_Rw1e5/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day070525_500_HaveFusion_NoRefine_Celeb3_EditedFusion_LR000625_Rw1e5/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person is eating his birthday cake" \
    --save_dir "./logs/IMAGES/Day070525_500_HaveFusion_NoRefine_Celeb3_EditedFusion_LR000625_Rw1e5/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day070525_500_HaveFusion_NoRefine_Celeb3_EditedFusion_LR000625_Rw1e5/learned_embeddings/learned_embeds.bin" \
    --prompt "a {} person with angry expression" \
    --save_dir "./logs/IMAGES/Day070525_500_HaveFusion_NoRefine_Celeb3_EditedFusion_LR000625_Rw1e5/TrainStep500_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42


python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day070525_500_HaveFusion_NoRefine_Celeb3_EditedFusion_LR000625_Rw1e5/learned_embeddings/learned_embeds-steps-320.bin" \
    --prompt "a photo of {} person" \
    --save_dir "./logs/IMAGES/Day070525_500_HaveFusion_NoRefine_Celeb3_EditedFusion_LR000625_Rw1e5/TrainStep320_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day070525_500_HaveFusion_NoRefine_Celeb3_EditedFusion_LR000625_Rw1e5/learned_embeddings/learned_embeds-steps-320.bin" \
    --prompt "a {} person is eating his birthday cake" \
    --save_dir "./logs/IMAGES/Day070525_500_HaveFusion_NoRefine_Celeb3_EditedFusion_LR000625_Rw1e5/TrainStep320_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42
python test_cross_init.py \
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \
    --num_inference_steps 50 \
    --learned_embedding_path "./logs/Day070525_500_HaveFusion_NoRefine_Celeb3_EditedFusion_LR000625_Rw1e5/learned_embeddings/learned_embeds-steps-320.bin" \
    --prompt "a {} person with angry expression" \
    --save_dir "./logs/IMAGES/Day070525_500_HaveFusion_NoRefine_Celeb3_EditedFusion_LR000625_Rw1e5/TrainStep320_TestStep50" \
    --num_images_per_prompt=10 \
    --n_iter=1 \
    --seed=42