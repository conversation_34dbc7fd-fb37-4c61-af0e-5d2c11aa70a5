#!/usr/bin/env python3
"""
Diagnostic script for analyzing identity preservation in Enhanced Textual Inversion training.

This script helps identify parameter imbalances and provides recommendations for
optimal identity preservation while maintaining knowledge preservation benefits.
"""

import argparse
import json
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from configs.experiment_config import (
    get_identity_focused_config, 
    get_progressive_training_configs,
    get_baseline_config
)


def analyze_parameter_balance(knowledge_preservation_weight, reg_weight, clip_lr, main_lr, training_steps):
    """Analyze parameter balance for identity preservation."""
    
    print("🔍 PARAMETER BALANCE ANALYSIS")
    print("=" * 50)
    
    # Calculate relative loss component strengths
    diffusion_strength = 1.0  # Always 1.0
    reg_strength = reg_weight * 100  # Typical embedding distance ~0.01
    kp_strength = knowledge_preservation_weight * 1.0  # Typical cosine distance ~1.0
    
    print(f"📊 Relative Loss Component Strengths:")
    print(f"   Diffusion Loss:           {diffusion_strength:.6f}")
    print(f"   Regularization Loss:      {reg_strength:.6f}")
    print(f"   Knowledge Preservation:   {kp_strength:.6f}")
    
    # Identify potential issues
    issues = []
    recommendations = []
    
    # Check knowledge preservation dominance
    if kp_strength > diffusion_strength * 0.1:
        issues.append("⚠️  Knowledge preservation may be too strong")
        recommendations.append("Reduce --knowledge_preservation_weight to 0.01-0.03")
    
    # Check training steps adequacy
    if training_steps < 1000 and kp_strength > 0.05:
        issues.append("⚠️  Insufficient training steps for knowledge preservation")
        recommendations.append("Increase --max_train_steps to 1500+ or reduce knowledge preservation")
    
    # Check CLIP learning rate
    if clip_lr > 5e-6 and kp_strength > 0.02:
        issues.append("⚠️  CLIP learning rate too high with knowledge preservation")
        recommendations.append("Reduce --clip_lr to 1e-6 to 2e-6")
    
    # Check regularization vs identity learning
    if reg_strength > diffusion_strength * 0.01:
        issues.append("⚠️  Regularization may be too strong")
        recommendations.append("Reduce --reg_weight to 1e-6 to 5e-6")
    
    # Print analysis results
    if issues:
        print(f"\n❌ IDENTIFIED ISSUES:")
        for issue in issues:
            print(f"   {issue}")
        
        print(f"\n💡 RECOMMENDATIONS:")
        for rec in recommendations:
            print(f"   {rec}")
    else:
        print(f"\n✅ Parameter balance looks good for identity preservation!")
    
    return len(issues) == 0


def generate_optimal_commands():
    """Generate optimal training commands for different scenarios."""
    
    print("\n🎯 OPTIMAL TRAINING COMMANDS")
    print("=" * 50)
    
    # Get configurations
    identity_config = get_identity_focused_config()
    progressive_configs = get_progressive_training_configs()
    
    base_cmd = """python train_cross_init.py \\
    --save_steps 300 \\
    --only_save_embeds \\
    --placeholder_token "<29525@c3>" \\
    --train_batch_size 2 \\
    --scale_lr \\
    --n_persudo_tokens 2 \\
    --train_data_dir "./examples/input_images/celeb3" \\
    --celeb_path "./examples/wiki_names_v2.txt" \\
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \\
    --resolution 512 \\
    --gradient_checkpointing \\
    --gradient_accumulation_steps 4"""
    
    print("🥇 RECOMMENDED: Identity-Focused Training")
    print("-" * 40)
    identity_cmd = f"""{base_cmd} \\
    --reg_weight "5e-6" \\
    --learning_rate 0.0005 \\
    --max_train_step 1500 \\
    --output_dir "./logs/identity_focused_training/learned_embeddings" \\
    --finetune_clip \\
    --clip_lr "2e-6" \\
    --clip_weight_decay 0.005 \\
    --use_knowledge_preservation \\
    --knowledge_preservation_weight 0.02"""
    print(identity_cmd)
    
    print("\n🏃 ALTERNATIVE: Progressive Training (Stage 1)")
    print("-" * 40)
    stage1_cmd = f"""{base_cmd} \\
    --reg_weight "1e-6" \\
    --learning_rate 0.0005 \\
    --max_train_step 800 \\
    --output_dir "./logs/stage1_identity_establishment/learned_embeddings" \\
    --validation_steps 100"""
    print(stage1_cmd)
    
    print("\n🔄 DIAGNOSTIC: Baseline Comparison")
    print("-" * 40)
    baseline_cmd = f"""{base_cmd} \\
    --reg_weight "1e-5" \\
    --learning_rate 0.000625 \\
    --max_train_step 1000 \\
    --output_dir "./logs/baseline_comparison/learned_embeddings" \\
    --validation_steps 100"""
    print(baseline_cmd)


def create_monitoring_script():
    """Create a script to monitor training progress."""
    
    monitoring_script = '''#!/usr/bin/env python3
"""
Real-time training monitor for identity preservation.
Run this alongside your training to track loss components.
"""

import json
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import time

def monitor_training(log_dir):
    """Monitor training logs for identity preservation issues."""
    
    log_file = Path(log_dir) / "logs" / "training_log.jsonl"
    
    if not log_file.exists():
        print(f"Log file not found: {log_file}")
        return
    
    # Read training logs
    logs = []
    with open(log_file, 'r') as f:
        for line in f:
            try:
                logs.append(json.loads(line))
            except:
                continue
    
    if not logs:
        print("No training logs found")
        return
    
    # Extract loss components
    steps = [log.get('step', 0) for log in logs]
    total_loss = [log.get('total_loss', 0) for log in logs]
    diffusion_loss = [log.get('diffusion_loss', 0) for log in logs]
    reg_loss = [log.get('regularization_loss', 0) for log in logs]
    kp_loss = [log.get('knowledge_preservation_loss', 0) for log in logs]
    
    # Create monitoring plot
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # Total loss
    ax1.plot(steps, total_loss, 'b-', linewidth=2)
    ax1.set_title('Total Loss')
    ax1.set_xlabel('Steps')
    ax1.set_ylabel('Loss')
    ax1.grid(True)
    
    # Loss components
    ax2.plot(steps, diffusion_loss, 'g-', label='Diffusion', linewidth=2)
    ax2.plot(steps, reg_loss, 'r-', label='Regularization', linewidth=2)
    ax2.plot(steps, kp_loss, 'orange', label='Knowledge Preservation', linewidth=2)
    ax2.set_title('Loss Components')
    ax2.set_xlabel('Steps')
    ax2.set_ylabel('Loss')
    ax2.legend()
    ax2.grid(True)
    
    # Loss ratios
    if len(diffusion_loss) > 0:
        reg_ratio = [r/d if d > 0 else 0 for r, d in zip(reg_loss, diffusion_loss)]
        kp_ratio = [k/d if d > 0 else 0 for k, d in zip(kp_loss, diffusion_loss)]
        
        ax3.plot(steps, reg_ratio, 'r-', label='Reg/Diffusion', linewidth=2)
        ax3.plot(steps, kp_ratio, 'orange', label='KP/Diffusion', linewidth=2)
        ax3.axhline(y=0.1, color='red', linestyle='--', alpha=0.5, label='Warning Level')
        ax3.set_title('Loss Ratios (vs Diffusion)')
        ax3.set_xlabel('Steps')
        ax3.set_ylabel('Ratio')
        ax3.legend()
        ax3.grid(True)
    
    # Identity preservation indicator
    if len(kp_loss) > 10:
        kp_trend = np.polyfit(steps[-10:], kp_loss[-10:], 1)[0]
        diff_trend = np.polyfit(steps[-10:], diffusion_loss[-10:], 1)[0]
        
        identity_score = max(0, min(1, 1 - abs(kp_trend) / abs(diff_trend) if diff_trend != 0 else 0))
        
        ax4.bar(['Identity Preservation'], [identity_score], 
                color='green' if identity_score > 0.7 else 'orange' if identity_score > 0.4 else 'red')
        ax4.set_ylim(0, 1)
        ax4.set_title(f'Identity Preservation Score: {identity_score:.2f}')
        ax4.grid(True)
    
    plt.tight_layout()
    plt.savefig(Path(log_dir) / 'training_monitor.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # Print warnings
    if len(kp_loss) > 0:
        avg_kp = np.mean(kp_loss[-10:]) if len(kp_loss) >= 10 else np.mean(kp_loss)
        avg_diff = np.mean(diffusion_loss[-10:]) if len(diffusion_loss) >= 10 else np.mean(diffusion_loss)
        
        if avg_kp > avg_diff * 0.1:
            print("⚠️  WARNING: Knowledge preservation loss is too high!")
            print("   Consider reducing --knowledge_preservation_weight")
        
        if avg_diff < 0.1:
            print("⚠️  WARNING: Diffusion loss is very low, possible overfitting")
            print("   Consider reducing training steps or increasing regularization")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        monitor_training(sys.argv[1])
    else:
        print("Usage: python monitor_training.py <log_directory>")
'''
    
    with open("monitor_training.py", "w") as f:
        f.write(monitoring_script)
    
    print(f"\n📊 Created monitoring script: monitor_training.py")
    print("Usage: python monitor_training.py <your_log_directory>")


def main():
    parser = argparse.ArgumentParser(description="Diagnose identity preservation issues")
    parser.add_argument("--knowledge_preservation_weight", type=float, default=0.1)
    parser.add_argument("--reg_weight", type=float, default=1e-5)
    parser.add_argument("--clip_lr", type=float, default=1e-5)
    parser.add_argument("--learning_rate", type=float, default=0.000625)
    parser.add_argument("--max_train_steps", type=int, default=500)
    parser.add_argument("--generate_commands", action="store_true", help="Generate optimal training commands")
    parser.add_argument("--create_monitor", action="store_true", help="Create training monitoring script")
    
    args = parser.parse_args()
    
    print("🔬 ENHANCED TEXTUAL INVERSION IDENTITY DIAGNOSIS")
    print("=" * 60)
    
    if args.generate_commands:
        generate_optimal_commands()
    
    if args.create_monitor:
        create_monitoring_script()
    
    # Analyze current parameters
    print(f"\n📋 ANALYZING YOUR CURRENT PARAMETERS:")
    print(f"   Knowledge Preservation Weight: {args.knowledge_preservation_weight}")
    print(f"   Regularization Weight: {args.reg_weight}")
    print(f"   CLIP Learning Rate: {args.clip_lr}")
    print(f"   Main Learning Rate: {args.learning_rate}")
    print(f"   Training Steps: {args.max_train_steps}")
    
    is_balanced = analyze_parameter_balance(
        args.knowledge_preservation_weight,
        args.reg_weight,
        args.clip_lr,
        args.learning_rate,
        args.max_train_steps
    )
    
    if not is_balanced:
        print(f"\n🎯 QUICK FIX FOR YOUR CURRENT SETUP:")
        print("Replace your current command with:")
        print("-" * 40)
        
        quick_fix_cmd = f"""python train_cross_init.py \\
    --save_steps 300 \\
    --only_save_embeds \\
    --placeholder_token "<29525@c3>" \\
    --train_batch_size 2 \\
    --scale_lr \\
    --n_persudo_tokens 2 \\
    --reg_weight "5e-6" \\
    --learning_rate 0.0005 \\
    --max_train_step 1500 \\
    --train_data_dir "./examples/input_images/celeb3" \\
    --celeb_path "./examples/wiki_names_v2.txt" \\
    --pretrained_model_name_or_path "stabilityai/stable-diffusion-2-1-base" \\
    --output_dir "./logs/identity_focused_fix/learned_embeddings" \\
    --resolution 512 \\
    --gradient_checkpointing \\
    --gradient_accumulation_steps 4 \\
    --finetune_clip \\
    --clip_lr "2e-6" \\
    --clip_weight_decay 0.005 \\
    --use_knowledge_preservation \\
    --knowledge_preservation_weight 0.02"""
        
        print(quick_fix_cmd)


if __name__ == "__main__":
    main()
