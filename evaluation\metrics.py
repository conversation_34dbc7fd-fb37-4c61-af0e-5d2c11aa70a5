"""
Comprehensive evaluation metrics for Enhanced Textual Inversion.

This module implements various metrics to evaluate the quality of personalized
text-to-image generation, including identity preservation, semantic understanding,
and generation quality.
"""

import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image
import clip
from transformers import CLIPProcessor, CLIPModel
from typing import List, Dict, Any, Optional
import os


class CLIPScoreMetric:
    """CLIP Score for measuring text-image alignment."""
    
    def __init__(self, device="cuda"):
        self.device = device
        self.model, self.preprocess = clip.load("ViT-B/32", device=device)
        self.model.eval()
    
    def compute_score(self, images: List[Image.Image], texts: List[str]) -> float:
        """Compute CLIP score between images and texts."""
        with torch.no_grad():
            # Preprocess images
            image_inputs = torch.stack([
                self.preprocess(img) for img in images
            ]).to(self.device)
            
            # Tokenize texts
            text_inputs = clip.tokenize(texts).to(self.device)
            
            # Get features
            image_features = self.model.encode_image(image_inputs)
            text_features = self.model.encode_text(text_inputs)
            
            # Normalize features
            image_features = F.normalize(image_features, dim=-1)
            text_features = F.normalize(text_features, dim=-1)
            
            # Compute cosine similarity
            scores = torch.diagonal(torch.mm(image_features, text_features.T))
            
            return scores.mean().item()


class IdentityPreservationMetric:
    """Metric for measuring identity preservation using face recognition."""
    
    def __init__(self, device="cuda"):
        self.device = device
        # Use CLIP for identity comparison (can be replaced with face recognition model)
        self.model, self.preprocess = clip.load("ViT-B/32", device=device)
        self.model.eval()
    
    def compute_similarity(self, reference_images: List[Image.Image], 
                          generated_images: List[Image.Image]) -> float:
        """Compute identity similarity between reference and generated images."""
        with torch.no_grad():
            # Preprocess images
            ref_inputs = torch.stack([
                self.preprocess(img) for img in reference_images
            ]).to(self.device)
            
            gen_inputs = torch.stack([
                self.preprocess(img) for img in generated_images
            ]).to(self.device)
            
            # Get image features
            ref_features = self.model.encode_image(ref_inputs)
            gen_features = self.model.encode_image(gen_inputs)
            
            # Normalize features
            ref_features = F.normalize(ref_features, dim=-1)
            gen_features = F.normalize(gen_features, dim=-1)
            
            # Compute average similarity
            similarities = []
            for ref_feat in ref_features:
                for gen_feat in gen_features:
                    sim = F.cosine_similarity(ref_feat.unsqueeze(0), gen_feat.unsqueeze(0))
                    similarities.append(sim.item())
            
            return np.mean(similarities)


class SemanticUnderstandingMetric:
    """Metric for evaluating semantic understanding and compositionality."""
    
    def __init__(self, device="cuda"):
        self.device = device
        self.clip_model, self.clip_preprocess = clip.load("ViT-B/32", device=device)
        self.clip_model.eval()
    
    def evaluate_compositionality(self, images: List[Image.Image], 
                                 base_prompts: List[str],
                                 attribute_prompts: List[str]) -> Dict[str, float]:
        """
        Evaluate how well the model composes the learned concept with different attributes.
        
        Args:
            images: Generated images
            base_prompts: Base prompts without attributes (e.g., "a photo of <concept>")
            attribute_prompts: Prompts with attributes (e.g., "a photo of <concept> smiling")
        """
        results = {}
        
        with torch.no_grad():
            # Preprocess images
            image_inputs = torch.stack([
                self.clip_preprocess(img) for img in images
            ]).to(self.device)
            
            image_features = self.clip_model.encode_image(image_inputs)
            image_features = F.normalize(image_features, dim=-1)
            
            # Test base concept understanding
            base_text_inputs = clip.tokenize(base_prompts).to(self.device)
            base_text_features = self.clip_model.encode_text(base_text_inputs)
            base_text_features = F.normalize(base_text_features, dim=-1)
            
            base_scores = torch.diagonal(torch.mm(image_features, base_text_features.T))
            results['base_concept_score'] = base_scores.mean().item()
            
            # Test attribute composition
            attr_text_inputs = clip.tokenize(attribute_prompts).to(self.device)
            attr_text_features = self.clip_model.encode_text(attr_text_inputs)
            attr_text_features = F.normalize(attr_text_features, dim=-1)
            
            attr_scores = torch.diagonal(torch.mm(image_features, attr_text_features.T))
            results['attribute_composition_score'] = attr_scores.mean().item()
            
            # Compositionality ratio (how well attributes are preserved)
            results['compositionality_ratio'] = results['attribute_composition_score'] / results['base_concept_score']
        
        return results


class KnowledgePreservationMetric:
    """Metric for evaluating knowledge preservation during fine-tuning."""
    
    def __init__(self, original_text_encoder, finetuned_text_encoder, tokenizer, device="cuda"):
        self.original_encoder = original_text_encoder
        self.finetuned_encoder = finetuned_text_encoder
        self.tokenizer = tokenizer
        self.device = device
        
        # Set to eval mode
        self.original_encoder.eval()
        self.finetuned_encoder.eval()
    
    def compute_semantic_drift(self, test_prompts: List[str]) -> Dict[str, float]:
        """
        Compute semantic drift by comparing embeddings from original and fine-tuned encoders.
        
        Args:
            test_prompts: List of general prompts to test knowledge preservation
        """
        results = {}
        similarities = []
        
        with torch.no_grad():
            for prompt in test_prompts:
                # Tokenize prompt
                inputs = self.tokenizer(
                    prompt,
                    padding="max_length",
                    max_length=self.tokenizer.model_max_length,
                    truncation=True,
                    return_tensors="pt",
                ).to(self.device)
                
                # Get embeddings from both encoders
                original_output = self.original_encoder(**inputs)
                finetuned_output = self.finetuned_encoder(**inputs)
                
                # Pool embeddings (mean pooling)
                original_pooled = original_output.last_hidden_state.mean(dim=1)
                finetuned_pooled = finetuned_output.last_hidden_state.mean(dim=1)
                
                # Compute cosine similarity
                similarity = F.cosine_similarity(original_pooled, finetuned_pooled, dim=1)
                similarities.append(similarity.item())
        
        results['mean_similarity'] = np.mean(similarities)
        results['std_similarity'] = np.std(similarities)
        results['min_similarity'] = np.min(similarities)
        results['semantic_drift'] = 1 - results['mean_similarity']
        
        return results


class ComprehensiveEvaluator:
    """Comprehensive evaluator combining all metrics."""
    
    def __init__(self, device="cuda"):
        self.device = device
        self.clip_score = CLIPScoreMetric(device)
        self.identity_preservation = IdentityPreservationMetric(device)
        self.semantic_understanding = SemanticUnderstandingMetric(device)
    
    def evaluate(self, 
                 generated_images: List[Image.Image],
                 prompts: List[str],
                 reference_images: Optional[List[Image.Image]] = None,
                 original_encoder=None,
                 finetuned_encoder=None,
                 tokenizer=None) -> Dict[str, Any]:
        """
        Perform comprehensive evaluation.
        
        Args:
            generated_images: List of generated images
            prompts: Corresponding prompts used for generation
            reference_images: Reference images for identity preservation
            original_encoder: Original text encoder for knowledge preservation
            finetuned_encoder: Fine-tuned text encoder
            tokenizer: Tokenizer for text encoding
        """
        results = {}
        
        # CLIP Score
        clip_score = self.clip_score.compute_score(generated_images, prompts)
        results['clip_score'] = clip_score
        
        # Identity Preservation (if reference images provided)
        if reference_images:
            identity_score = self.identity_preservation.compute_similarity(
                reference_images, generated_images
            )
            results['identity_preservation'] = identity_score
        
        # Semantic Understanding
        base_prompts = [p.replace(" smiling", "").replace(" outdoors", "") for p in prompts]
        semantic_results = self.semantic_understanding.evaluate_compositionality(
            generated_images, base_prompts, prompts
        )
        results.update(semantic_results)
        
        # Knowledge Preservation (if encoders provided)
        if original_encoder and finetuned_encoder and tokenizer:
            test_prompts = [
                "a photo of a person",
                "a beautiful landscape",
                "a cat sitting on a table",
                "a red car on the street",
                "a sunset over the ocean"
            ]
            
            knowledge_metric = KnowledgePreservationMetric(
                original_encoder, finetuned_encoder, tokenizer, self.device
            )
            knowledge_results = knowledge_metric.compute_semantic_drift(test_prompts)
            results.update(knowledge_results)
        
        return results


def load_images_from_directory(directory: str) -> List[Image.Image]:
    """Load all images from a directory."""
    images = []
    for filename in os.listdir(directory):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            img_path = os.path.join(directory, filename)
            images.append(Image.open(img_path).convert('RGB'))
    return images


if __name__ == "__main__":
    # Example usage
    evaluator = ComprehensiveEvaluator()
    
    # Mock data for testing
    generated_images = [Image.new('RGB', (512, 512), color='red') for _ in range(4)]
    prompts = ["a photo of a person"] * 4
    
    results = evaluator.evaluate(generated_images, prompts)
    print("Evaluation Results:", results)
