1. Tổng quan kiến trúc hệ thống
+----------------------------------+
|       Textual Inversion Flow     |
+----------------------------------+
         |
         v
+------------------+    +------------------+    +------------------+
|  Initialization  | -> |     Training     | -> |    Inference     |
+------------------+    +------------------+    +------------------+



2. Chi tiết quá trình khởi tạo (Initialization)

+--------------------------------------------+
|              Initialization                |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| Input: Ảnh mẫu + Token mới "<new_token>"   |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 1. Tải model Stable Diffusion pre-trained  |
|    - Text Encoder (CLIP)                   |
|    - VAE                                   |
|    - UNet                                  |
|    - Tokenizer                             |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 2. Thêm token mới vào tokenizer            |
|    - tokenizer.add_tokens("<new_token>")   |
|    - text_encoder.resize_token_embeddings()|
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 3. Khởi tạo embedding cho token mới        |
|    - Phương pháp 1: token_cross_init()     |
|      (Từ danh sách token có nghĩa)         |
|    - Phương pháp 2: celeb_names_cross_init()|
|      (Từ danh sách tên người nổi tiếng)    |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 4. Tạo dataset từ ảnh đầu vào              |
|    - TextualInversionDataset(data_root)    |
+--------------------------------------------+



3. Chi tiết quá trình training

+--------------------------------------------+
|               Training Loop                |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| Input:                                     |
| - Ảnh mẫu (của concept cần học)            |
| - Token mới đã khởi tạo embedding          |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| For each epoch:                            |
|   For each batch:                          |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 1. Xử lý ảnh đầu vào:                      |
|    - Resize, normalize                     |
|    - Chuyển sang tensor                    |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 2. Tạo prompt với token mới:               |
|    "a photo of <new_token> person"         |
|    => Tokenize => input_ids                |
+--------------------------------------------+
         |
         v
+-------------------+                +-------------------+
| 3a. Encode ảnh:   |                | 3b. Encode text:  |
| - VAE.encode()    |                | - text_encoder()  |
| => latents        |                | => text_embeds    |
+-------------------+                +-------------------+
         |                                    |
         v                                    v
+--------------------------------------------+
| 4. Thêm nhiễu vào latents:                 |
|    - noise = torch.randn_like(latents)     |
|    - noisy_latents = add_noise(latents,    |
|                     noise, timesteps)       |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 5. Forward qua UNet:                       |
|    - pred = unet(noisy_latents,           |
|                  timesteps,                |
|                  text_embeds)              |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 6. Tính loss:                              |
|    - loss_recon = MSE(pred, target)        |
|    - loss_reg = dist(learned_embeds,       |
|                       initial_embeds)       |
|    - loss = loss_recon + loss_reg * weight |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 7. Cập nhật model:                         |
|    - loss.backward()                       |
|    - optimizer.step()                      |
|    - Chỉ cập nhật embedding của token mới  |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 8. Validation định kỳ:                     |
|    - Tạo ảnh test với prompt có token mới  |
|    - Lưu ảnh để kiểm tra                   |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| Output: Token embedding đã học             |
|         (learned_embeds.bin)               |
+--------------------------------------------+



4. Chi tiết quy trình inference

+--------------------------------------------+
|               Inference                    |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| Input:                                     |
| - Prompt chứa token mới: "a photo of {}"   |
| - Embedding đã học (learned_embeds.bin)    |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 1. Tải model Stable Diffusion              |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 2. Thêm token mới vào tokenizer            |
|    và gán embedding đã học                 |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 3. Chuẩn bị prompt:                        |
|    "a photo of <new_token> person"         |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 4. Tạo ảnh với Stable Diffusion pipeline:  |
|    - Tokenize prompt                       |
|    - Encode text                           |
|    - Khuếch tán từ nhiễu -> latent         |
|    - Decode latent -> ảnh                  |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| Output: Ảnh chứa concept đã học            |
+--------------------------------------------+






5. Chi tiết về quy trình khởi tạo embedding

+--------------------------------------------+
|         Token Cross Initialization         |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| Input:                                     |
| - Danh sách token có nghĩa                 |
|   hoặc tên người nổi tiếng                 |
+--------------------------------------------+
         |
         v
+------------------------------------+
| Phương pháp 1: token_cross_init() |
+------------------------------------+
| 1. Tokenize các token đầu vào      |
| 2. Lấy embedding của các token     |
| 3. Trả về embedding trung bình     |
+------------------------------------+
         |
         v
+------------------------------------+
| Phương pháp 2: celeb_cross_init() |
+------------------------------------+
| 1. Đọc danh sách tên người nổi    |
|    tiếng từ file                  |
| 2. Chọn ngẫu nhiên n_pseudo tên   |
| 3. Lấy embedding của từng tên     |
| 4. Trả về các embedding này       |
+------------------------------------+


6. Chi tiết về TextualInversionDataset

+--------------------------------------------+
|         TextualInversionDataset            |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| Input:                                     |
| - Thư mục chứa ảnh mẫu                     |
| - Tokenizer                                |
| - Placeholder tokens                        |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| __init__():                                |
| 1. Thu thập đường dẫn ảnh                  |
| 2. Tạo danh sách templates prompt          |
|    (VD: "a photo of {}")                   |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| __getitem__(i):                            |
| 1. Đọc ảnh thứ i                           |
| 2. Chọn ngẫu nhiên template prompt         |
| 3. Thay placeholder bằng token mới        |
|    VD: "a photo of <new_token> person"     |
| 4. Tokenize prompt -> input_ids            |
| 5. Xử lý ảnh: resize, normalize            |
| 6. Trả về dictionary với pixel_values      |
|    và input_ids                            |
+--------------------------------------------+



7. Tương tác giữa các module

+---------------+    +----------------+    +----------------+
| CLI Arguments | -> | parse_args()   | -> | Training Args  |
+---------------+    +----------------+    +----------------+
                                |
                                v
+-----------------+    +----------------+    +----------------+
| Training Images | -> | Dataset Class  | -> | DataLoader     |
+-----------------+    +----------------+    +----------------+
                                                     |
                                                     v
+----------------+    +----------------+    +----------------+
| Text Encoder   | <- | Training Loop  | -> | UNet           |
+----------------+    +----------------+    +----------------+
        ^                     |                     ^
        |                     v                     |
+----------------+    +----------------+    +----------------+
| Tokenizer      |    | Optimizer      |    | VAE            |
+----------------+    +----------------+    +----------------+
        ^                     |
        |                     v
+----------------+    +----------------+    +----------------+
| Token Init     | -> | Learned Embed  | -> | Inference      |
+----------------+    +----------------+    +----------------+


8. Chi tiết quy trình tính loss

+--------------------------------------------+
|           Loss Calculation                 |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| Input:                                     |
| - model_pred: Dự đoán của UNet            |
| - target: noise/velocity target            |
| - placeholder_token_ids: ID của token mới  |
| - initialize_embeds: Embedding ban đầu     |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 1. Diffusion Loss:                         |
|    - loss_diff = F.mse_loss(model_pred,    |
|                             target)        |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 2. Regularization Loss:                    |
|    - current_embeds = text_encoder.weight  |
|                      [placeholder_token_ids]|
|    - loss_reg = F.pairwise_distance(       |
|                  current_embeds,           |
|                  initialize_embeds)        |
+--------------------------------------------+
         |
         v
+--------------------------------------------+
| 3. Total Loss:                             |
|    - loss = loss_diff + loss_reg * weight  |
+--------------------------------------------+


