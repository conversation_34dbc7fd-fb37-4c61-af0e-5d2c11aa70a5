import torch
import torch.nn as nn
import torch.nn.functional as F

class CrossAttentionFusion(nn.Module):
    def __init__(self, dim=1024):
        super().__init__()
        self.attention = nn.MultiheadAttention(embed_dim=dim, num_heads=8, batch_first=True)
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)
        
    def forward(self, text_emb, img_emb):
        """
        text_emb: [seq_len_text, dim]
        img_emb: [seq_len_img, dim]
        """
        # Thêm batch dimension
        text_emb = text_emb.unsqueeze(0)  # [1, seq_len_text, dim]
        img_emb = img_emb.unsqueeze(0)    # [1, seq_len_img, dim]

        # Cross-Attention: Query=text, Key=Value=image
        attn_output, _ = self.attention(
            query=text_emb,
            key=img_emb,
            value=img_emb
        )

        # LayerNorm + Residual
        attn_output = self.norm1(attn_output + text_emb)
        
        return attn_output.squeeze(0)  # [seq_len_text, dim]

class FusionTransformer(nn.Module):
    def __init__(self, dim=1024):
        super().__init__()
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=dim,
            nhead=8,
            dim_feedforward=dim*4,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=2)
        self.norm = nn.LayerNorm(dim)
        
    def forward(self, text_emb, img_emb):
        """
        text_emb: [seq_len_text, dim]
        img_emb: [seq_len_img, dim]
        """
        # Ghép chuỗi và thêm batch dim
        combined = torch.cat([text_emb, img_emb], dim=0).unsqueeze(0)  # [1, seq_len_total, dim]
        
        # Transformer Encoder
        fused = self.transformer(combined)  # [1, seq_len_total, dim]
        
        # Residual norm
        fused = self.norm(fused)
        
        # Cắt ra phần của text
        return fused[0, :text_emb.shape[0], :]  # [seq_len_text, dim]

# Helper class to mimic CLIPTextEncoderOutput
class CLIPTextEncoderOutput:
    def __init__(self, last_hidden_state):
        self.last_hidden_state = last_hidden_state
        
class CLIPTextEncoderFinetune(nn.Module):
    def __init__(self, text_encoder, dim=1024):
        super().__init__()
        self.text_encoder = text_encoder
        
        # Thêm các layer để finetune
        self.projection = nn.Sequential(
            nn.Linear(dim, dim),
            nn.LayerNorm(dim),
            nn.GELU(),
            nn.Linear(dim, dim)
        )
        
    # Uỷ quyền phương thức get_input_embeddings cho text_encoder gốc
    def get_input_embeddings(self):
        return self.text_encoder.get_input_embeddings()

    # Uỷ quyền phương thức resize_token_embeddings cho text_encoder gốc
    def resize_token_embeddings(self, new_num_tokens):
        self.text_encoder.resize_token_embeddings(new_num_tokens)

    def forward(self, input_ids=None, attention_mask=None, inputs_embeds=None):
        # Lấy output từ CLIP text encoder gốc
        # Sử dụng inputs_embeds nếu có (khi dùng initialize_embeds)
        if inputs_embeds is not None:
             outputs = self.text_encoder(inputs_embeds=inputs_embeds, attention_mask=attention_mask)
        else:
             outputs = self.text_encoder(input_ids, attention_mask=attention_mask)

        hidden_states = outputs.last_hidden_state

        # Áp dụng projection lên hidden states
        projected_hidden_states = self.projection(hidden_states)

        # Trả về đối tượng giống output của CLIPTextModel gốc
        return CLIPTextEncoderOutput(last_hidden_state=projected_hidden_states)